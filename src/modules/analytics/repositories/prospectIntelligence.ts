import { convertMinutesToMS, convertSecondsToMS } from '@core/util'
import { ProspectIntelligenceModel } from '@modules/analytics/models/prospectIntelligence'
import { PiCrmSummary } from '@modules/analytics/types/piCrmSummary'
import {
  CASA_ACTIVITIES,
  FilterQuery,
  PROSPECT_ACTIVITIES,
  ProspectActivities,
  ProspectActivitiesTypes,
  TourResponse
} from '@modules/analytics/types/prospectIntelligence'
import { DXSettingServices } from '@modules/communities/types/dxSetting'
import { SpaceType } from '@modules/communities/types/spaceType'
import { ProspectModel } from '@modules/prospects/models/prospect'
import { WalkaboutModel } from '@modules/sgt/models/walkabout'
import { parseISO, sub } from 'date-fns'
import { Types } from 'mongoose'
import { getPiCRMTourAggregate } from './aggregates/getPiCRMTourAggregate'
import { logDebug } from '@core/log'

const UNIT = 'day'
const AMOUNT = 90

const location = 'analytics/services/prospectIntelligence'

const getActivitiesByEventType = (eventType) => {
  let activities: ProspectActivities[]
  switch (eventType) {
    case ProspectActivitiesTypes.SGT: {
      activities = [
        ProspectActivities.SGT_BOOKED,
        ProspectActivities.SGT_CANCELLED,
        ProspectActivities.SGT_RESCHEDULED,
        ProspectActivities.SGT_STARTED
      ]
      break
    }
    case ProspectActivitiesTypes.APPOINTMENT: {
      activities = [ProspectActivities.IN_PERSON_TOUR]
      break
    }
    case ProspectActivitiesTypes.INQUIRY: {
      activities = [
        ProspectActivities.INQUIRY,
        ProspectActivities.SGT_INQUIRY,
        ProspectActivities.WELCOME_SITE_INQUIRY
      ]
      break
    }
    case ProspectActivitiesTypes.VIRTUAL_TOUR: {
      activities = [ProspectActivities.VIRTUAL_TOUR]
      break
    }
    case ProspectActivitiesTypes.CASA: {
      activities = CASA_ACTIVITIES
      break
    }
    default: {
      activities = PROSPECT_ACTIVITIES
    }
  }
  return activities
}

const getProspectActivityQuery = (
  activity: ProspectActivities | ProspectActivities[],
  eventType: ProspectActivitiesTypes
) => {
  let activities = getActivitiesByEventType(eventType)
  const activityFilter = typeof activity === 'string' ? [activity] : activity

  if (activityFilter) {
    activities = activities.filter((item) => activityFilter.includes(item))
  }

  return {
    $or: [
      {
        appId: {
          $in: ['welcome-site', 'web-viewer', 'casa']
        },
        eventAction: {
          $in: activities?.filter(
            (activity) => activity !== ProspectActivities.VIRTUAL_TOUR
          )
        }
      },
      ...((activities.includes(ProspectActivities.VIRTUAL_TOUR) && [
        {
          appId: 'web-viewer',
          eventType: 'page'
        }
      ]) ||
        [])
    ]
  }
}

const getProspectActivityType = (actionField: string) => {
  return {
    $switch: {
      branches: [
        {
          case: {
            $or: [
              {
                $eq: [actionField, ProspectActivities.SGT_BOOKED]
              },
              {
                $eq: [actionField, ProspectActivities.SGT_CANCELLED]
              },
              {
                $eq: [actionField, ProspectActivities.SGT_RESCHEDULED]
              },
              {
                $eq: [actionField, ProspectActivities.SGT_STARTED]
              }
            ]
          },
          then: ProspectActivitiesTypes.SGT
        },
        {
          case: {
            $eq: [actionField, ProspectActivities.IN_PERSON_TOUR]
          },
          then: ProspectActivitiesTypes.APPOINTMENT
        },
        {
          case: {
            $or: [
              {
                $eq: [actionField, ProspectActivities.INQUIRY]
              },
              {
                $eq: [actionField, ProspectActivities.SGT_INQUIRY]
              },
              {
                $eq: [actionField, ProspectActivities.WELCOME_SITE_INQUIRY]
              }
            ]
          },
          then: ProspectActivitiesTypes.INQUIRY
        },
        {
          case: {
            $eq: [actionField, ProspectActivities.VIRTUAL_TOUR]
          },
          then: ProspectActivitiesTypes.VIRTUAL_TOUR
        },
        {
          case: {
            $or: [
              {
                $eq: [actionField, ProspectActivities.CASA_EMAIL_IN]
              },
              {
                $eq: [actionField, ProspectActivities.CASA_EMAIL_OUT]
              },
              {
                $eq: [actionField, ProspectActivities.CASA_EMAIL_START]
              },
              {
                $eq: [actionField, ProspectActivities.CASA_EMAIL_HANDOFF]
              },
              {
                $eq: [actionField, ProspectActivities.CASA_CHAT_CONVERSATION]
              }
            ]
          },
          then: ProspectActivitiesTypes.CASA
        }
      ],
      default: '$noval'
    }
  }
}

export const getRoomsViewed = async (query: any): Promise<any> => {
  const { amount = AMOUNT, unit = UNIT, ...defaultQuery } = query

  return ProspectIntelligenceModel.aggregate([
    {
      $match: {
        $expr: {
          $gte: [
            '$eventTimestamp',
            {
              $dateSubtract: { startDate: '$$NOW', unit, amount: +amount }
            }
          ]
        },
        deletedAt: null,
        ...defaultQuery,
        roomId: { $exists: true, $ne: null },
        roomName: { $exists: true, $ne: null },
        duration: { $gt: 0 }
      }
    },
    {
      $group: {
        _id: {
          sessionId: '$sessionId',
          roomId: '$roomId',
          prospectId: '$prospectId',
          startedAt: '$startedAt',
          endedAt: '$endedAt'
        },
        metadata: { $last: '$$ROOT' }
      }
    },
    {
      $group: {
        _id: {
          sessionId: '$_id.sessionId',
          prospectId: '$_id.prospectId',
          roomId: '$_id.roomId'
        },
        duration: { $sum: '$metadata.duration' },
        metadata: { $last: '$metadata' }
      }
    },
    {
      $group: {
        _id: {
          roomName: '$metadata.roomName'
        },
        count: { $sum: 1 },
        duration: { $sum: '$duration' }
      }
    },
    {
      $sort: { duration: -1, '_id.roomName': 1 }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$duration' },
        count: { $sum: '$count' },
        rooms: {
          $push: {
            roomName: '$_id.roomName',
            duration: '$duration'
          }
        }
      }
    },
    {
      $project: {
        _id: 0,
        total: 1,
        count: 1,
        rooms: { $slice: ['$rooms', 0, 8] }
      }
    }
  ])
}

export const getTotalSessions = async (query: any): Promise<any> => {
  const { amount = AMOUNT, unit = UNIT, ...defaultQuery } = query

  return ProspectIntelligenceModel.aggregate([
    {
      $match: {
        $expr: {
          $gte: [
            '$eventTimestamp',
            {
              $dateSubtract: { startDate: '$$NOW', unit, amount: +amount }
            }
          ]
        },
        deletedAt: null,
        ...defaultQuery,
        spaceId: { $exists: true, $ne: null },
        sessionId: { $exists: true, $ne: null },
        duration: { $exists: true, $gt: 0 }
      }
    },
    {
      $group: {
        _id: {
          sessionId: '$sessionId',
          spaceId: '$spaceId',
          prospectId: '$prospectId',
          startedAt: '$startedAt',
          endedAt: '$endedAt'
        }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: 1 }
      }
    },
    {
      $project: {
        _id: 0,
        total: 1
      }
    }
  ])
}

export const getTotalEngagement = async (query: any): Promise<any> => {
  const { amount = AMOUNT, unit = UNIT, ...defaultQuery } = query

  return ProspectIntelligenceModel.aggregate([
    {
      $match: {
        $expr: {
          $gte: [
            '$eventTimestamp',
            {
              $dateSubtract: { startDate: '$$NOW', unit, amount: +amount }
            }
          ]
        },
        deletedAt: null,
        ...defaultQuery,
        sessionId: { $exists: true, $ne: null },
        spaceId: { $exists: true, $ne: null },
        duration: { $exists: true, $gt: 0 }
      }
    },
    {
      $group: {
        _id: {
          sessionId: '$sessionId',
          roomId: '$roomId',
          spaceId: '$spaceId',
          communityId: '$communityId',
          organizaitonId: '$organizationId',
          prospectId: '$prospectId',
          startedAt: '$startedAt',
          endedAt: '$endedAt'
        },
        duration: { $last: '$duration' }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$duration' }
      }
    },
    {
      $project: {
        _id: 0,
        total: 1
      }
    }
  ])
}

export const getTotalSpaces = async (query: any): Promise<any> => {
  const {
    spaceType = SpaceType.Unit,
    amount = AMOUNT,
    unit = UNIT,
    ...defaultQuery
  } = query

  return ProspectIntelligenceModel.aggregate([
    {
      $match: {
        $expr: {
          $gte: [
            '$eventTimestamp',
            {
              $dateSubtract: { startDate: '$$NOW', unit, amount: +amount }
            }
          ]
        },
        deletedAt: null,
        ...defaultQuery,
        spaceId: { $exists: true, $ne: null },
        spaceType
      }
    },
    {
      $group: {
        _id: {
          spaceId: '$spaceId'
        }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: 1 }
      }
    },
    {
      $project: {
        _id: 0,
        total: 1
      }
    }
  ])
}

export const getIdealBedrooms = async (query: any): Promise<any> => {
  const { unit = UNIT, amount = AMOUNT, ...defaultQuery } = query

  const results = await ProspectIntelligenceModel.aggregate([
    {
      $match: {
        $expr: {
          $gte: [
            '$eventTimestamp',
            {
              $dateSubtract: { startDate: '$$NOW', unit, amount: +amount }
            }
          ]
        },
        ...defaultQuery,
        deletedAt: null,
        spaceId: { $exists: true, $ne: null },
        spaceBedrooms: { $exists: true, $ne: null },
        spaceType: SpaceType.Unit,
        sessionId: { $exists: true, $ne: null },
        duration: { $exists: true, $gt: 0 }
      }
    },
    {
      $group: {
        _id: {
          sessionId: '$sessionId',
          roomId: '$roomId',
          spaceId: '$spaceId',
          communityId: '$communityId',
          organizationId: '$organizationId',
          prospectId: '$prospectId',
          startedAt: '$startedAt',
          endedAt: '$endedAt'
        },
        metadata: { $last: '$$ROOT' }
      }
    },
    {
      $group: {
        _id: {
          sessionId: '$metadata.sessionId',
          spaceId: '$metadata.spaceId'
        },
        duration: { $sum: '$metadata.duration' },
        metadata: { $last: '$metadata' }
      }
    },
    {
      $group: {
        _id: {
          spaceId: '$metadata.spaceId'
        },
        duration: { $sum: '$metadata.duration' },
        spaceBedrooms: { $last: '$metadata.spaceBedrooms' }
      }
    },
    {
      $bucket: {
        groupBy: '$spaceBedrooms',
        boundaries: [0, 1, 1.5, 2, 2.5, 3],
        default: '3',
        output: {
          duration: { $sum: '$duration' }
        }
      }
    },
    {
      $sort: { duration: -1 }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$duration' },
        idealBedrooms: {
          $push: {
            bucket: { $toString: '$_id' },
            duration: '$duration'
          }
        }
      }
    },
    {
      $project: {
        _id: 0,
        total: 1,
        idealBedrooms: 1
      }
    }
  ])

  return results
}

export const getIdealPrices = async (query: any): Promise<any> => {
  const { unit = UNIT, amount = AMOUNT, ...defaultQuery } = query

  const results = await ProspectIntelligenceModel.aggregate([
    {
      $match: {
        $expr: {
          $gte: [
            '$eventTimestamp',
            {
              $dateSubtract: { startDate: '$$NOW', unit, amount: +amount }
            }
          ]
        },
        deletedAt: null,
        ...defaultQuery,
        spaceId: { $exists: true, $ne: null },
        spaceName: { $exists: true, $ne: null },
        spaceRentalPrice: { $exists: true, $ne: null, $gt: 0 },
        sessionId: { $exists: true, $ne: null },
        duration: { $exists: true, $gt: 0 }
      }
    },
    {
      $group: {
        _id: {
          sessionId: '$sessionId',
          roomId: '$roomId',
          spaceId: '$spaceId',
          communityId: '$communityId',
          organizationId: '$organizationId',
          prospectId: '$prospectId',
          startedAt: '$startedAt',
          endedAt: '$endedAt'
        },
        metadata: { $last: '$$ROOT' }
      }
    },
    {
      $group: {
        _id: {
          sessionId: '$metadata.sessionId',
          spaceId: '$metadata.spaceId'
        },
        duration: { $sum: '$metadata.duration' },
        metadata: { $last: '$metadata' }
      }
    },
    {
      $group: {
        _id: {
          spaceId: '$metadata.spaceId'
        },
        duration: { $sum: '$metadata.duration' },
        price: { $last: '$metadata.spaceRentalPrice' }
      }
    },
    {
      $bucket: {
        groupBy: '$price',
        boundaries: [0, 1000, 2000, 3000, 4000, 5000],
        default: '5000',
        output: {
          duration: { $sum: '$duration' },
          avgPrice: { $avg: '$price' }
        }
      }
    },
    {
      $addFields: {
        price: {
          $multiply: [{ $ceil: { $divide: ['$avgPrice', 50] } }, 50]
        }
      }
    },
    {
      $sort: { duration: -1 }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$duration' },
        idealPrices: {
          $push: {
            bucket: { $toString: '$_id' },
            duration: '$duration',
            prices: '$prices',
            price: '$price'
          }
        }
      }
    },
    {
      $project: {
        _id: 0,
        total: 1,
        idealPrices: 1
      }
    }
  ])
  return results?.length ? results[0] : { total: 0, idealPrices: [] }
}

export const getIdealFloorPlans = async (query: any) => {
  const { unit = UNIT, amount = AMOUNT, ...defaultQuery } = query

  const results = await ProspectIntelligenceModel.aggregate([
    {
      $match: {
        $expr: {
          $gte: [
            '$eventTimestamp',
            {
              $dateSubtract: { startDate: '$$NOW', unit, amount: +amount }
            }
          ]
        },
        deletedAt: null,
        ...defaultQuery,
        spaceFloorPlan: { $exists: true, $ne: null },
        spaceType: SpaceType.Unit,
        sessionId: { $exists: true, $ne: null },
        duration: { $exists: true, $gt: 0 }
      }
    },
    {
      $group: {
        _id: {
          sessionId: '$sessionId',
          roomId: '$roomId',
          spaceId: '$spaceId',
          communityId: '$communityId',
          organizationId: '$organizationId',
          prospectId: '$prospectId',
          startedAt: '$startedAt',
          endedAt: '$endedAt'
        },
        metadata: { $last: '$$ROOT' }
      }
    },
    {
      $group: {
        _id: {
          sessionId: '$metadata.sessionId',
          spaceId: '$metadata.spaceId'
        },
        duration: { $sum: '$metadata.duration' },
        metadata: { $last: '$metadata' }
      }
    },
    {
      $group: {
        _id: {
          spaceId: '$metadata.spaceId'
        },
        duration: { $sum: '$metadata.duration' },
        metadata: { $last: '$metadata' }
      }
    },
    {
      $group: {
        _id: {
          spaceFloorPlan: '$metadata.spaceFloorPlan'
        },
        duration: { $sum: '$duration' }
      }
    },
    {
      $sort: { duration: -1, '_id.spaceFloorPlan': 1 }
    },
    {
      $group: {
        _id: null,
        total: { $sum: '$duration' },
        floorPlans: {
          $push: {
            spaceFloorPlan: '$_id.spaceFloorPlan',
            duration: '$duration'
          }
        }
      }
    },
    {
      $project: {
        _id: 0,
        total: 1,
        idealFloorPlans: { $slice: ['$floorPlans', 0, 8] }
      }
    }
  ])

  return results
}

export type GetProspectsIntelligenceQuery = {
  organizationId: any
  communityId: any
  unit?: string
  amount?: number
  prospectId?: string
  orderBy?: string
  order?: string
  offset?: number
  limit?: number
}

export const getProspectActivities = async (query: any): Promise<any> => {
  const { offset = 0, limit = 10, groupBy, queryFilter, orderBy, order } = query

  const {
    prospectId = { $exists: true, $ne: null },
    unit = UNIT,
    amount = AMOUNT,
    activity,
    eventType,
    ...defaultQuery
  } = queryFilter
  const activityQuery = getProspectActivityQuery(activity, eventType)
  const projectionPrefix = (groupBy && 'recentEvent.') || ''

  const aggregation = [
    {
      $match: {
        $expr: {
          $gte: [
            '$eventTimestamp',
            {
              $dateSubtract: {
                startDate: '$$NOW',
                unit,
                amount: +amount
              }
            }
          ]
        },
        deletedAt: null,
        ...defaultQuery,
        prospectId,
        ...activityQuery
      }
    },
    {
      $sort: { eventTimestamp: -1 }
    },
    ...((groupBy && [
      {
        $group: {
          _id: '$prospectEmail',
          recentEvent: { $first: '$$ROOT' }
        }
      }
    ]) ||
      []),
    {
      $project: {
        _id: `$${projectionPrefix}_id`,
        prospect: {
          _id: `$${projectionPrefix}prospectId`,
          firstName: `$${projectionPrefix}prospectFirstName`,
          lastName: `$${projectionPrefix}prospectLastName`,
          email: `$${projectionPrefix}prospectEmail`,
          phone: `$${projectionPrefix}prospectPhone`
        },
        event: {
          _id: `$${projectionPrefix}webEventId`,
          appId: `$${projectionPrefix}appId`,
          type: getProspectActivityType(`$${projectionPrefix}eventAction`),
          action: `$${projectionPrefix}eventAction`,
          timestamp: `$${projectionPrefix}eventTimestamp`
        },
        space: {
          _id: `$${projectionPrefix}spaceId`,
          unit: `$${projectionPrefix}spaceName`,
          type: `$${projectionPrefix}spaceType`,
          floorPlan: {
            name: `$${projectionPrefix}spaceFloorPlan`
          },
          price: `$${projectionPrefix}spaceRentalPrice`,
          address: {
            street: `$${projectionPrefix}spaceAddress`
          }
        },
        community: {
          _id: `$${projectionPrefix}communityId`,
          name: `$${projectionPrefix}communityName`
        },
        organization: {
          _id: `$${projectionPrefix}organizationId`,
          name: `$${projectionPrefix}organizationName`
        },
        walkabout: {
          _id: `$${projectionPrefix}walkaboutId`,
          date: `$${projectionPrefix}walkaboutDate`,
          dateString: `$${projectionPrefix}walkaboutDateString`,
          startMinute: `$${projectionPrefix}walkaboutStartMinute`,
          endMinute: `$${projectionPrefix}walkaboutEndMinute`
        },
        appointment: {
          _id: `$${projectionPrefix}appointmentId`,
          date: `$${projectionPrefix}appointmentDate`,
          startTime: `$${projectionPrefix}appointmentStartTime`,
          endTime: `$${projectionPrefix}appointmentEndTime`
        }
      }
    },
    ...((orderBy && [
      {
        $sort: {
          [orderBy]: order === 'desc' ? -1 : 1
        }
      }
    ]) ||
      []),
    {
      $skip: +offset
    },
    {
      $limit: +limit
    }
  ]

  return ProspectIntelligenceModel.aggregate(<[]>aggregation)
}

export const countProspectActivities = async (query: any): Promise<any> => {
  const { groupBy } = query
  const {
    unit = UNIT,
    amount = AMOUNT,
    activity,
    eventType,
    prospectId = { $exists: true, $ne: null },
    ...defaultQuery
  } = query.queryFilter

  let dateFrom = {}
  if (query.startDate && query.endDate) {
    dateFrom = {
      eventTimestamp: {
        $gte: new Date(query.startDate),
        $lte: new Date(query.endDate)
      }
    }
  } else {
    dateFrom = {
      $expr: {
        $gte: [
          '$eventTimestamp',
          {
            $dateSubtract: {
              startDate: '$$NOW',
              unit,
              amount: +amount
            }
          }
        ]
      }
    }
  }
  const activityQuery = getProspectActivityQuery(activity, eventType)
  const [result] = await ProspectIntelligenceModel.aggregate([
    {
      $match: {
        ...dateFrom,
        deletedAt: null,
        ...defaultQuery,
        prospectId,
        ...activityQuery
      }
    },
    ...((groupBy && [
      {
        $group: {
          _id: '$prospectEmail'
        }
      }
    ]) ||
      []),
    {
      $group: {
        _id: null,
        count: { $sum: 1 }
      }
    }
  ])

  return result?.count || 0
}

export const getNewestProspectsRecentActivity = async (
  query: any
): Promise<any> => {
  const {
    offset = 0,
    limit = 7,
    orderBy = 'event.timestamp',
    order = 'desc',
    queryFilter
  } = query

  const {
    unit = 'day',
    amount = 7,
    activity,
    eventType,
    ...defaultQuery
  } = queryFilter
  const activityQuery = getProspectActivityQuery(activity, eventType)

  return ProspectModel.aggregate([
    {
      $match: {
        ...defaultQuery,
        $expr: {
          $gte: [
            '$createdAt',
            {
              $dateSubtract: {
                startDate: '$$NOW',
                unit,
                amount: +amount
              }
            }
          ]
        }
      }
    },
    {
      $sort: { createdAt: -1 }
    },
    {
      $skip: +offset
    },
    {
      $limit: +limit
    },
    {
      $group: {
        _id: '$email',
        metadata: { $first: '$$ROOT' }
      }
    },
    {
      $lookup: {
        from: 'prospectIntelligence',
        let: { prospectEmail: '$_id' },
        pipeline: [
          {
            $match: {
              ...defaultQuery,
              $expr: {
                $and: [
                  {
                    $eq: ['$prospectEmail', '$$prospectEmail']
                  }
                ]
              },
              ...activityQuery
            }
          },
          {
            $sort: { eventTimestamp: -1 }
          },
          {
            $limit: 1
          }
        ],
        as: 'recentActivity'
      }
    },
    {
      $unwind: {
        path: '$recentActivity',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $project: {
        _id: '$recentActivity._id',
        prospect: {
          _id: '$metadata._id',
          email: '$metadata.email',
          phone: '$metadata.phone',
          firstName: '$metadata.firstName',
          lastName: '$metadata.lastName',
          moveInDate: '$metadata.moveInDate',
          updatedAt: '$metadata.updatedAt'
        },
        event: {
          _id: '$recentActivity.webEventId',
          appId: '$recentActivity.appId',
          type: getProspectActivityType('$recentActivity.eventAction'),
          action: '$recentActivity.eventAction',
          timestamp: '$recentActivity.eventTimestamp'
        },
        space: {
          _id: '$recentActivity.spaceId',
          unit: '$recentActivity.spaceName',
          type: '$recentActivity.spaceType',
          floorPlan: {
            name: '$recentActivity.spaceFloorPlan'
          },
          price: '$recentActivity.spaceRentalPrice',
          address: {
            street: '$recentActivity.spaceAddress'
          }
        },
        community: {
          _id: '$recentActivity.communityId',
          name: '$recentActivity.communityName'
        },
        organization: {
          _id: '$recentActivity.organizationId',
          name: '$recentActivity.organizationName'
        },
        walkabout: {
          _id: '$recentActivity.walkaboutId',
          date: '$recentActivity.walkaboutDate',
          dateString: '$recentActivity.walkaboutDateString',
          startMinute: '$recentActivity.walkaboutStartMinute',
          endMinute: '$recentActivity.walkaboutEndMinute'
        },
        appointment: {
          _id: '$recentActivity.appointmentId',
          date: '$recentActivity.appointmentDate',
          startTime: '$recentActivity.appointmentStartTime',
          endTime: '$recentActivity.appointmentEndTime'
        }
      }
    },
    {
      $sort: { [orderBy]: order === 'asc' ? 1 : -1 }
    }
  ])
}

export const countNewestProspectsRecentActivity = async (
  query: any
): Promise<number> => {
  const { queryFilter } = query
  const { unit = 'day', amount = 7, ...defaultQuery } = queryFilter
  let dateForm = {}
  if (query.startDate && query.endDate) {
    dateForm = {
      createdAt: {
        $gte: new Date(query.startDate),
        $lte: new Date(query.endDate)
      }
    }
  } else {
    dateForm = {
      $expr: {
        $gte: [
          '$createdAt',
          {
            $dateSubtract: {
              startDate: '$$NOW',
              unit,
              amount: +amount
            }
          }
        ]
      }
    }
  }
  const [result] = await ProspectModel.aggregate([
    {
      $match: {
        ...defaultQuery,
        ...dateForm
      }
    },
    {
      $sort: { createdAt: -1 }
    },
    {
      $group: {
        _id: '$email',
        metadata: { $first: '$$ROOT' }
      }
    },
    {
      $group: {
        _id: null,
        total: { $sum: 1 }
      }
    }
  ])

  return result ? result.total : 0
}

export const countProspectsIntelligence = async (query: any): Promise<any> => {
  const {
    unit = UNIT,
    amount = AMOUNT,
    ignorePiScore = false,
    ...defaultFilter
  } = query.queryFilter

  const unitKey = unit + 's'

  const eventTimestamp = sub(new Date(), { [unitKey]: amount })

  const [result] = await ProspectIntelligenceModel.aggregate(
    [
      {
        $match: {
          $expr: {
            $gte: ['$eventTimestamp', eventTimestamp]
          },
          deletedAt: null,
          ...defaultFilter,
          spaceId: { $exists: true, $ne: null },
          prospectId: { $exists: true, $ne: null },
          appId: {
            $in: ['welcome-site', 'web-viewer', 'casa']
          },
          eventAction: { $exists: true, $ne: 'Prospect Inactive' },
          eventType: { $exists: true, $ne: 'identify' },
          spaceType: 'unit',
          ...(!ignorePiScore && { piScore: { $exists: true, $gte: 0 } })
        }
      },
      {
        $group: {
          _id: {
            prospectEmail: '$prospectEmail'
          }
        }
      },
      {
        $group: {
          _id: null,
          count: { $sum: 1 }
        }
      }
    ],
    { hint: 'communityId_1_organizationId_1_prospectId_1_eventTimestamp_1' }
  )

  return result?.count || 0
}

export const getProspectsIntelligence = async (query: any): Promise<any> => {
  const datetime = new Date()
  const now = parseISO(datetime.toISOString())
  const {
    offset = 0,
    limit = 10,
    orderBy = 'piScore',
    order = 'desc',
    queryFilter
  } = query
  const {
    unit = UNIT,
    amount = AMOUNT,
    prospectId = { $exists: true, $ne: null },
    spaceType = SpaceType.Unit,
    ignorePiScore = false,
    ...defaultQuery
  } = queryFilter

  const orderByKey = orderBy?.includes('prospect')
    ? `metadata.${orderBy}`
    : orderBy

  const commonMatchCriteria = {
    ...defaultQuery,
    appId: {
      $in: ['welcome-site', 'web-viewer', 'casa']
    },
    ...(prospectId && { prospectId }),
    spaceType,
    eventAction: { $exists: true, $ne: 'Prospect Inactive' },
    eventType: { $exists: true, $ne: 'identify' },
    spaceId: { $exists: true, $ne: null },
    ...(!ignorePiScore && { piScore: { $exists: true, $gte: 0 } })
  }

  const unitKey = unit + 's'

  const eventTimestamp = sub(now, { [unitKey]: amount })
  const hotnessLeadEventTimestamp = sub(now, { days: 2 })

  return ProspectIntelligenceModel.aggregate(
    [
      /**
       * Stage - Filter by organization scope and community scope
       */
      {
        $match: {
          $expr: {
            $gte: ['$eventTimestamp', eventTimestamp]
          },
          deletedAt: null,
          ...commonMatchCriteria
        }
      },
      /**
       * Stage - Group by real sessions where they have startedAt and endedAt
       * so every row of these events will have the same duration
       * and we can that the $first duration
       */
      {
        $group: {
          _id: {
            sessionId: '$sessionId',
            roomId: '$roomId',
            spaceId: '$spaceId',
            communityId: '$communityId',
            organizationId: '$organizationId',
            prospectId: '$prospectId',
            startedAt: '$startedAt',
            endedAt: '$endedAt'
          },
          metadata: { $first: '$$ROOT' }
        }
      },
      /**
       * Stage - Group them by space and sessions
       * and sum the duration of each session
       */
      {
        $group: {
          _id: {
            sessionId: '$_id.sessionId',
            prospectId: '$_id.prospectId',
            spaceId: '$_id.spaceId'
          },
          duration: { $sum: '$metadata.duration' },
          // store duration only for the last 2 days for pi score calculation
          piScoreDuration: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$metadata.spaceType', SpaceType.Unit] },
                    {
                      $gte: [
                        '$metadata.eventTimestamp',
                        hotnessLeadEventTimestamp
                      ]
                    },
                    {
                      $lte: ['$metadata.eventTimestamp', now]
                    }
                  ]
                },
                '$metadata.duration',
                0
              ]
            }
          },
          metadata: { $first: '$metadata' }
        }
      },
      /**
       * Stage - Group them by space and prospect
       */
      {
        $group: {
          _id: {
            prospectId: '$_id.prospectId',
            spaceId: '$_id.spaceId'
          },
          duration: { $sum: '$duration' },
          piScoreDuration: { $sum: '$piScoreDuration' },
          metadata: { $first: '$metadata' }
        }
      },
      /**
       * Stage - Group them by prospect and take the total spaces visited and total duration
       */
      {
        $group: {
          _id: {
            prospectId: '$metadata.prospectId'
          },
          metadata: { $first: '$metadata' },
          viewTime: { $sum: '$duration' },
          piScoreViewTime: { $sum: '$piScoreDuration' },
          unitsViewed: { $sum: 1 },
          piScoreUnitsViewed: {
            $sum: {
              $cond: [
                {
                  $and: [
                    { $eq: ['$metadata.spaceType', 'unit'] },
                    {
                      $gte: [
                        '$metadata.eventTimestamp',
                        hotnessLeadEventTimestamp
                      ]
                    },
                    {
                      $lte: ['$metadata.eventTimestamp', now]
                    }
                  ]
                },
                1,
                0
              ]
            }
          }
        }
      },
      /**
       * Stage - Group them by prospect email and take the total spaces visited and total duration
       */
      {
        $group: {
          _id: {
            prospectEmail: '$metadata.prospectEmail'
          },
          viewTime: { $sum: '$viewTime' },
          piScoreViewTime: { $sum: '$piScoreViewTime' },
          unitsViewed: { $sum: '$unitsViewed' },
          piScoreUnitsViewed: { $sum: '$piScoreUnitsViewed' }
        }
      },
      /**
       * Stage - Find the very last event of each prospect to check their piScore
       */
      {
        $lookup: {
          from: 'prospectIntelligence',
          let: { groupProspectEmail: '$_id.prospectEmail' },
          pipeline: [
            {
              $match: {
                ...commonMatchCriteria,
                $expr: {
                  $and: [
                    {
                      $eq: ['$prospectEmail', '$$groupProspectEmail']
                    }
                  ]
                }
              }
            },
            {
              $sort: { eventTimestamp: -1 }
            },
            {
              $limit: 1
            }
          ],
          as: 'lastEvent'
        }
      },
      {
        $unwind: {
          path: '$lastEvent',
          preserveNullAndEmptyArrays: true
        }
      },
      /**
       * Stage - add piScore and piStatus based on the last event of each prospect
       * if the last event is older than 2 days, then the piScore and piStatus will be 0 and COLD
       * else the piScore and piStatus will be the last event's piScore and piStatus
       */
      {
        $addFields: {
          piScore: {
            $cond: {
              if: {
                $or: [
                  {
                    $and: [
                      { $eq: ['$piScoreUnitsViewed', 1] },
                      { $gte: ['$piScoreViewTime', convertMinutesToMS(1)] },
                      { $lte: ['$piScoreViewTime', convertMinutesToMS(5)] }
                    ]
                  },
                  {
                    $and: [
                      { $eq: ['$piScoreUnitsViewed', 2] },
                      { $gte: ['$piScoreViewTime', convertSecondsToMS(30)] },
                      { $lt: ['$piScoreViewTime', convertMinutesToMS(1)] }
                    ]
                  },
                  {
                    $and: [
                      { $gte: ['$piScoreUnitsViewed', 3] },
                      { $gte: ['$piScoreViewTime', convertSecondsToMS(30)] },
                      { $lt: ['$piScoreViewTime', convertMinutesToMS(1)] }
                    ]
                  }
                ]
              },
              then: 1,
              else: {
                $cond: {
                  if: {
                    $or: [
                      {
                        $and: [
                          { $eq: ['$piScoreUnitsViewed', 2] },
                          { $gte: ['$piScoreViewTime', convertMinutesToMS(1)] },
                          { $lte: ['$piScoreViewTime', convertMinutesToMS(5)] }
                        ]
                      },
                      {
                        $and: [
                          { $gte: ['$piScoreUnitsViewed', 3] },
                          { $gte: ['$piScoreViewTime', convertMinutesToMS(1)] },
                          { $lte: ['$piScoreViewTime', convertMinutesToMS(5)] }
                        ]
                      }
                    ]
                  },
                  then: 2,
                  else: 0
                }
              }
            }
          }
        }
      },
      {
        $addFields: {
          piStatus: {
            $cond: {
              if: {
                $gte: ['$piScore', 2]
              },
              then: 'HOT',
              else: {
                $cond: {
                  if: {
                    $gte: ['$piScore', 1]
                  },
                  then: 'WARM',
                  else: 'COLD'
                }
              }
            }
          }
        }
      },
      /**
       * Stage - project the object just like a prospect object with extra info
       */
      {
        $project: {
          _id: '$lastEvent.prospectId',
          firstName: '$lastEvent.prospectFirstName',
          lastName: '$lastEvent.prospectLastName',
          email: '$lastEvent.prospectEmail',
          phone: '$lastEvent.prospectPhone',
          community: {
            _id: '$lastEvent.communityId',
            name: '$lastEvent.communityName'
          },
          organization: {
            _id: '$lastEvent.organizationId',
            name: '$lastEvent.organizationName'
          },
          viewTime: 1,
          unitsViewed: 1,
          lastActivity: '$lastEvent.eventTimestamp',
          piStatus: 1,
          piScore: 1
        }
      },
      {
        $sort: {
          [orderByKey]: order === 'asc' ? 1 : -1
        }
      },
      {
        $skip: +offset
      },
      {
        $limit: +limit
      }
    ],
    { hint: 'communityId_1_organizationId_1_prospectId_1_eventTimestamp_1' }
  )
}

export const getHotnessLeadScore = async (query: any) => {
  const { ignorePiScore = false, ...defaultQuery } = query

  return ProspectIntelligenceModel.aggregate([
    {
      $match: {
        $expr: {
          $gte: [
            '$eventTimestamp',
            {
              $dateSubtract: { startDate: '$$NOW', unit: 'day', amount: 2 }
            }
          ]
        },
        deletedAt: null,
        ...defaultQuery,
        spaceId: { $exists: true, $ne: null },
        appId: {
          $in: ['welcome-site', 'web-viewer', 'casa']
        },
        prospectId: {
          $exists: true,
          $ne: null
        },
        spaceType: 'unit',
        eventAction: { $exists: true, $ne: 'Prospect Inactive' },
        eventType: { $exists: true, $ne: 'identify' },
        ...(!ignorePiScore && { piScore: { $exists: true, $gte: 0 } })
      }
    },
    {
      $sort: {
        eventTimestamp: -1
      }
    },
    /**
     * Stage - Group by real sessions where they have startedAt and endedAt
     * so every row of these events will have the same duration
     * and we can that the $first duration
     */
    {
      $group: {
        _id: {
          sessionId: '$sessionId',
          roomId: '$roomId',
          spaceId: '$spaceId',
          communityId: '$communityId',
          organizationId: '$organizationId',
          prospectId: '$prospectId',
          startedAt: '$startedAt',
          endedAt: '$endedAt'
        },
        metadata: { $first: '$$ROOT' }
      }
    },
    /**
     * Stage - Group them by space and sessions
     * and sum the duration of each session
     */
    {
      $group: {
        _id: {
          sessionId: '$_id.sessionId',
          prospectId: '$_id.prospectId',
          spaceId: '$_id.spaceId'
        },
        duration: { $sum: '$metadata.duration' },
        metadata: { $first: '$metadata' }
      }
    },
    /**
     * Stage - Group them by space and prospect
     */
    {
      $group: {
        _id: {
          prospectId: '$_id.prospectId',
          spaceId: '$_id.spaceId'
        },
        duration: { $sum: '$duration' },
        metadata: { $first: '$metadata' }
      }
    },
    /**
     * Stage - Group them by prospect and take the total spaces visited and total duration
     */
    {
      $group: {
        _id: {
          prospectId: '$metadata.prospectId'
        },
        metadata: { $first: '$metadata' },
        viewTime: { $sum: '$duration' },
        unitsViewed: { $sum: 1 }
      }
    },
    /**
     * Stage - Group them by prospect email and take the total spaces visited and total duration
     */
    {
      $group: {
        _id: {
          prospectEmail: '$metadata.prospectEmail'
        },
        viewTime: { $sum: '$viewTime' },
        unitsViewed: { $sum: '$unitsViewed' },
        prospectFirstName: { $first: '$metadata.prospectFirstName' },
        prospectLastName: { $first: '$metadata.prospectLastName' },
        unitViewTimeByCommunity: {
          $push: {
            communityId: '$metadata.communityId',
            communityName: '$metadata.communityName',
            viewTime: '$viewTime'
          }
        }
      }
    },
    /**
     * Stage - add piScore and piStatus based on the last event of each prospect
     * if the last event is older than 2 days, then the piScore and piStatus will be 0 and COLD
     * else the piScore and piStatus will be the last event's piScore and piStatus
     */
    {
      $addFields: {
        piScore: {
          $cond: {
            if: {
              $or: [
                {
                  $and: [
                    { $eq: ['$unitsViewed', 1] },
                    { $gte: ['$viewTime', convertMinutesToMS(1)] },
                    { $lte: ['$viewTime', convertMinutesToMS(5)] }
                  ]
                },
                {
                  $and: [
                    { $eq: ['$unitsViewed', 2] },
                    { $gte: ['$viewTime', convertSecondsToMS(30)] },
                    { $lt: ['$viewTime', convertMinutesToMS(1)] }
                  ]
                },
                {
                  $and: [
                    { $gte: ['$unitsViewed', 3] },
                    { $gte: ['$viewTime', convertSecondsToMS(30)] },
                    { $lt: ['$viewTime', convertMinutesToMS(1)] }
                  ]
                }
              ]
            },
            then: 1,
            else: {
              $cond: {
                if: {
                  $or: [
                    {
                      $and: [
                        { $eq: ['$unitsViewed', 2] },
                        { $gte: ['$viewTime', 60000] },
                        { $lte: ['$viewTime', 300000] }
                      ]
                    },
                    {
                      $and: [
                        { $gte: ['$unitsViewed', 3] },
                        { $gte: ['$viewTime', 60000] },
                        { $lte: ['$viewTime', 300000] }
                      ]
                    }
                  ]
                },
                then: 2,
                else: 0
              }
            }
          }
        }
      }
    },
    {
      $addFields: {
        piStatus: {
          $cond: {
            if: {
              $gte: ['$piScore', 2]
            },
            then: 'HOT',
            else: {
              $cond: {
                if: {
                  $gte: ['$piScore', 1]
                },
                then: 'WARM',
                else: 'COLD'
              }
            }
          }
        }
      }
    },
    {
      $project: {
        _id: 0,
        piScore: 1,
        piStatus: 1,
        prospectFirstName: 1,
        prospectLastName: 1,
        unitViewTimeByCommunity: 1,
        prospectEmail: '$_id.prospectEmail'
      }
    },
    { $sort: { piScore: -1 } }
  ])
}

export const findProspectIntelligenceById = async (id: string) => {
  return ProspectIntelligenceModel.findById(id).lean()
}

export const getStreamingPiDaily = async (query: FilterQuery) => {
  return ProspectIntelligenceModel.aggregate([
    {
      $match: {
        eventTimestamp: {
          $gte: new Date(query.startDate),
          $lte: new Date(query.endDate)
        },
        deletedAt: null,
        communityId: { $in: query.communityIds },
        spaceId: { $exists: true, $ne: null },
        appId: {
          $in: ['welcome-site', 'web-viewer', 'casa']
        },
        prospectId: { $exists: true },
        spaceType: 'unit',
        eventAction: { $exists: true, $ne: 'Prospect Inactive' },
        eventType: { $exists: true, $ne: 'identify' }
      }
    },
    {
      $sort: { eventTimestamp: -1 }
    },
    {
      $group: {
        _id: {
          sessionId: '$sessionId',
          roomId: '$roomId',
          spaceId: '$spaceId',
          communityId: '$communityId',
          organizationId: '$organizationId',
          prospectId: '$prospectId',
          startedAt: '$startedAt',
          endedAt: '$endedAt'
        },
        metadata: { $first: '$$ROOT' }
      }
    },
    {
      $group: {
        _id: {
          communityId: '$_id.communityId',
          sessionId: '$_id.sessionId',
          prospectId: '$_id.prospectId',
          spaceId: '$_id.spaceId'
        },
        duration: { $sum: '$metadata.duration' },
        metadata: { $first: '$metadata' }
      }
    },
    {
      $group: {
        _id: {
          communityId: '$_id.communityId',
          prospectId: '$_id.prospectId',
          spaceId: '$_id.spaceId'
        },
        duration: { $sum: '$duration' },
        metadata: { $first: '$metadata' }
      }
    },
    {
      $group: {
        _id: {
          communityId: '$_id.communityId',
          prospectId: '$_id.prospectId'
        },
        virtualToursViewed: { $sum: 1 },
        viewTime: { $sum: '$duration' },
        metadata: { $first: '$metadata' }
      }
    },

    {
      $group: {
        _id: '$_id.communityId',
        viewTime: { $sum: '$viewTime' },
        virtualToursViewed: { $sum: '$virtualToursViewed' },
        communityId: { $first: '$metadata.communityId' },
        communityName: { $first: '$metadata.communityName' },
        metadata: { $first: '$metadata' }
      }
    },
    { $sort: { virtualToursViewed: -1 } },
    {
      $group: {
        _id: null,
        viewTime: { $sum: '$viewTime' },
        virtualToursViewed: { $sum: '$virtualToursViewed' },
        mostViewCommunity: { $first: '$virtualToursViewed' },
        communityId: { $first: '$metadata.communityId' },
        communityName: { $first: '$metadata.communityName' },
        metadata: { $first: '$metadata' }
      }
    }
  ])
}

export const getNewlyAddedProspect = async (minutes) => {
  return ProspectIntelligenceModel.aggregate([
    {
      $match: {
        $expr: {
          $gte: [
            '$eventTimestamp',
            {
              $dateSubtract: {
                startDate: '$$NOW',
                unit: 'minute',
                amount: minutes
              }
            }
          ]
        },
        deletedAt: null,
        appId: {
          $in: ['welcome-site', 'web-viewer', 'casa']
        },
        eventType: { $exists: true, $ne: 'identify' },
        prospectId: { $exists: true },
        spaceId: { $exists: true, $ne: null },
        eventAction: { $exists: true, $ne: 'Prospect Inactive' },
        piScore: { $exists: true, $gte: 0 }
      }
    },
    {
      $sort: { eventTimestamp: -1 }
    },
    {
      $group: {
        _id: {
          communityId: '$communityId',
          prospectEmail: '$prospectEmail'
        },
        metadata: { $first: '$$ROOT' }
      }
    },
    {
      $project: {
        communityId: '$_id.communityId',
        organizationId: '$metadata.organizationId',
        communityName: '$metadata.communityName',
        prospectEmail: '$metadata.prospectEmail',
        prospectPhone: '$metadata.prospectPhone',
        prospectFirstName: '$metadata.prospectFirstName',
        prospectLastName: '$metadata.prospectLastName',
        eventTimestamp: '$metadata.eventTimestamp',
        _id: 0
      }
    }
  ])
}

export const getPrevDayProspect = async (query: FilterQuery) => {
  return ProspectIntelligenceModel.aggregate([
    {
      $match: {
        eventTimestamp: {
          $gte: new Date(query.startDate),
          $lte: new Date(query.endDate)
        },
        deletedAt: null,
        communityId: { $in: query.communityIds },
        spaceId: { $exists: true, $ne: null },
        appId: {
          $in: ['welcome-site', 'web-viewer', 'casa']
        },
        prospectId: { $exists: true, $ne: null },
        prospectEmail: { $exists: true, $ne: null },
        eventAction: { $exists: true, $ne: 'Prospect Inactive' },
        eventType: { $exists: true, $ne: 'identify' }
      }
    },
    {
      $sort: { eventTimestamp: -1 }
    },
    {
      $group: {
        _id: {
          prospectEmail: '$prospectEmail'
        },
        metadata: { $first: '$$ROOT' }
      }
    },
    {
      $group: {
        _id: null,
        prospectEmail: { $addToSet: '$_id.prospectEmail' }
      }
    }
  ])
}

export const SgtBookedCount = async (query: FilterQuery) => {
  const [result] = await WalkaboutModel.aggregate([
    {
      $match: {
        createdAt: {
          $gte: new Date(query.startDate),
          $lte: new Date(query.endDate)
        }
      }
    },
    {
      $lookup: {
        from: 'spaces',
        localField: 'spaceId',
        foreignField: '_id',
        as: 'space'
      }
    },
    {
      $unwind: {
        path: '$space'
      }
    },
    {
      $match: {
        'space.community._id': { $in: query.communityIds }
      }
    },
    {
      $group: {
        _id: null,
        count: { $sum: 1 }
      }
    }
  ])

  return result?.count || 0
}

export const getConsolidatedVirtualToursForDxService = async (
  timeInHours: number,
  dxService: DXSettingServices
) => {
  return ProspectIntelligenceModel.aggregate([
    {
      $match: {
        eventTimestamp: {
          $gte: new Date(Date.now() - timeInHours * 60 * 60 * 1000)
        },
        deletedAt: null,
        appId: 'web-viewer',
        prospectId: { $exists: true, $ne: null },
        duration: { $gt: 0 },
        spaceType: SpaceType.Unit
      }
    },
    {
      $sort: { eventTimestamp: -1 }
    },
    {
      $group: {
        _id: {
          spaceId: '$spaceId',
          prospectId: '$prospectId',
          startedAt: '$startedAt',
          endedAt: '$endedAt',
          roomId: '$roomId',
          sessionId: '$sessionId'
        },
        metadata: {
          $first: '$$ROOT'
        }
      }
    },
    {
      $group: {
        _id: {
          prospectId: '$_id.prospectId',
          spaceId: '$_id.spaceId',
          sessionId: '$_id.sessionId'
        },
        duration: { $sum: '$metadata.duration' },
        metadata: { $first: '$metadata' }
      }
    },
    {
      $project: {
        prospectId: '$_id.prospectId',
        spaceId: '$_id.spaceId',
        sessionId: '$_id.sessionId',
        startedAt: '$metadata.startedAt',
        duration: 1
      }
    },
    {
      $lookup: {
        from: 'externallinks',
        localField: 'prospectId',
        foreignField: 'objectId',
        as: 'externalLink'
      }
    },
    {
      $match: {
        'externalLink.service': dxService
      }
    }
  ])
}

export const getRoiReportTours = async (
  communityId: string,
  startDate: Date,
  endDate: Date
): Promise<TourResponse[]> => {
  const result = await ProspectIntelligenceModel.aggregate([
    {
      $match: {
        eventTimestamp: { $gte: startDate, $lte: endDate },
        deletedAt: null,
        communityId: new Types.ObjectId(communityId),
        appId: 'web-viewer',
        sessionId: { $exists: true, $ne: null },
        anonymousId: { $exists: true, $ne: null },
        duration: { $gt: 0 }
      }
    },
    {
      $group: {
        _id: {
          anonymousId: '$anonymousId',
          sessionId: '$sessionId',
          roomId: '$roomId',
          spaceId: '$spaceId',
          communityId: '$communityId',
          organizationId: '$organizationId',
          prospectId: '$prospectId',
          startedAt: '$startedAt',
          endedAt: '$endedAt'
        },
        metadata: { $first: '$$ROOT' }
      }
    },
    {
      $group: {
        _id: {
          sessionId: '$_id.sessionId',
          anonymousId: '$_id.anonymousId',
          prospectId: '$_id.prospectId',
          spaceId: '$_id.spaceId'
        },
        duration: { $sum: '$metadata.duration' },
        metadata: { $first: '$metadata' }
      }
    },
    {
      $group: {
        _id: {
          anonymousId: '$_id.anonymousId',
          prospectId: '$_id.prospectId',
          spaceId: '$_id.spaceId'
        },
        duration: { $sum: '$duration' },
        metadata: { $first: '$metadata' }
      }
    },
    {
      $project: {
        anonymousId: '$_id.anonymousId',
        prospectId: '$_id.prospectId',
        spaceId: '$_id.spaceId',
        duration: 1
      }
    }
  ])

  return result
}

export const getPiCrmTours = async (
  startDate: string,
  endDate: string,
  communityId: string
): Promise<PiCrmSummary[]> => {
  const query = getPiCRMTourAggregate(startDate, endDate, communityId)
  logDebug(location, 'getPiCrmTours query:', query)
  return ProspectIntelligenceModel.aggregate(query)
}
