import { logDebug, logError, logInfo, logWarn } from '@core/log'
import { FindOptions, Query } from '@core/repositories/CrudRepository'
import {
  convertCoordinateToPitchAndYaw,
  flattenObjectKeys,
  hotspotToPosition
} from '@core/util'
import { addOrUpdateExternalLinkBy } from '@modules/communities/repositories/externalLink'
import { findCommunityById } from '@modules/communities/services/community'
import { markScanRequestAsUploaded } from '@modules/communities/services/scanRequest'
import {
  removeSpacesPrices,
  sendScanEmails
} from '@modules/communities/services/space'
import { Building } from '@modules/communities/types/building'
import {
  ExternalLink,
  ExternalLinkCollection,
  ExternalLinkSpaceX
} from '@modules/communities/types/externalLink'
import { Id, ObjectId } from '@modules/communities/types/id'
import { Space } from '@modules/communities/types/space'
import { SpaceType } from '@modules/communities/types/spaceType'
import { linkDevicesInOrder } from '@modules/sgt/services/accessDevice'
import {
  publishSpaceEvent,
  SpaceEventType
} from '@modules/spacex/events/spaceEvents'
import { spaceRepositoryMongoDb } from '@modules/spacex/repositories/spaceRepository'
import { findDisplayPrice } from '@modules/spacex/services/price'
import { CreateOrUpdateSpace, SpaceFromDistance } from '../types/space'
import { setBuilding } from './buildings'
import {
  processVirtualTour,
  triggerEnhancementPipeline
} from './virtualTourService'
import {
  findScanRequestsByQuery,
  updateScanRequests
} from '@modules/communities/repositories/scanRequest'
import { ScanRequest } from '@modules/communities/types/scanRequest'

const location = 'spacex-space-service'

export const addOrUpdateSpaceById = async (
  id: string,
  dto: CreateOrUpdateSpace,
  building?: Building,
  externalLinks?: ExternalLinkSpaceX[],
  processImage?: boolean,
  options?: { includeDeactivated: boolean },
  scheduledFor?: Date,
  version?: string | undefined
): Promise<Space> => {
  logInfo(location, 'Adding or updating space', { id, dto, building, version })

  if (id) {
    return await update(
      id,
      dto,
      building,
      processImage,
      externalLinks,
      options,
      scheduledFor,
      version
    )
  }

  return await create(
    dto,
    dto.community?._id.toString(),
    building,
    externalLinks,
    version
  )
}

export const create = async (
  space: Partial<Space>,
  communityId: string,
  building: Building,
  partialExternalLinks: ExternalLinkSpaceX[] = [],
  version?: string | undefined
): Promise<Space> => {
  logDebug(location, 'Creating space', {
    space,
    communityId,
    building,
    partialExternalLinks
  })

  const community = await findCommunityById(communityId)
  logDebug(location, 'Found community', { community })

  space.community = {
    _id: community._id,
    name: community.name,
    organization: community.organization,
    ...community.communityInfo,
    ...community.communityStyle,
    showContactForm: community.showContactForm,
    canBypassContactForm: community.canBypassContactForm,
    displayPriceField: community.displayPriceField,
    isActive: community.isActive,
    showAddress: community.showAddress
  }

  if (space.rentPrices?.length) {
    space.displayPrice = findDisplayPrice({
      displayPriceField: community.displayPriceField,
      rentPrices: space.rentPrices,
      pricesMetadata: space.pricesMetadata
    })
    logDebug(location, 'Found display price', {
      displayPrice: space.displayPrice
    })
  }

  if (space.isVisible === false) {
    space.sgtEnabled = false
  }

  space.building = await setBuilding(
    building,
    space,
    community,
    undefined,
    version
  )

  const createdSpace = await spaceRepositoryMongoDb.create(
    flattenObjectKeys(space) as Space
  )

  const externalLinks = partialExternalLinks.map((externalLink) => ({
    ...externalLink,
    objectId: createdSpace._id.toString(),
    communityId: community._id.toString(),
    collectionPath: ExternalLinkCollection.Spaces
  }))

  try {
    await createExternalLinks(externalLinks)
    logDebug(location, 'Created external links', { externalLinks })
  } catch (err) {
    logDebug(location, 'Error creating external links', { error: err })
    logError(
      location,
      `Error: creating external links for space ${createdSpace._id}`,
      {
        message: err.message,
        externalLinks
      }
    )
  }

  logDebug(location, 'Created space', { createdSpace })

  try {
    await publishSpaceEvent(
      createdSpace._id.toString(),
      undefined,
      undefined,
      SpaceEventType.CREATED
    )
  } catch (err) {
    logDebug(location, 'Error: to publish space message', { error: err })
    logError(location, 'Error: to publish space message', {
      error: err.message,
      space
    })
  }

  return createdSpace
}

export const update = async (
  id: string,
  dto: CreateOrUpdateSpace,
  building?: Building,
  processImage?: boolean,
  partialExternalLinks: ExternalLinkSpaceX[] = [],
  options?: { includeDeactivated: boolean },
  scheduledFor?: Date,
  version?: string | undefined
): Promise<Space> => {
  logDebug(location, 'Updating space', { id, dto, building })

  const existingSpace = await spaceRepositoryMongoDb.findById(id, options)

  logDebug(location, 'Found existing space', { existingSpace })
  if (!existingSpace) {
    throw new Error(`Error: Space with id ${id} not found`)
  }

  const community = await findCommunityById(
    existingSpace.community?._id?.toString()
  )
  if (!community) {
    throw new Error(
      `Error: Community with id ${existingSpace.community?._id} not found`
    )
  }
  logDebug(location, 'Found community', { community })

  if (dto?.nodes?.length) {
    logDebug(
      location,
      `Nodes have changed for space with id ${id}, calculating rotation...`,
      dto.nodes
    )

    dto.nodes.forEach((node) => {
      if (!node._id) {
        node._id = new ObjectId()
      }
      if (node.nodeLinks?.length) {
        node.nodeLinks.forEach((nodeLink) => {
          if (nodeLink.position && !nodeLink.rotation) {
            nodeLink.rotation = convertCoordinateToPitchAndYaw(
              nodeLink.position
            )
          }

          if (nodeLink.rotation && processImage) {
            nodeLink.position = hotspotToPosition(
              nodeLink.rotation.pitch,
              nodeLink.rotation.yaw
            )
          }
        })
      }
    })

    if (processImage) {
      await sendScanEmails({
        communityId: existingSpace.community._id.toString(),
        street:
          existingSpace.building?.address?.street ??
          existingSpace.address?.street,
        unit: dto.unit ?? existingSpace.unit
      })

      const processVirtualTourResult = await processVirtualTour(
        {
          ...existingSpace,
          nodes: dto.nodes
        },
        community
      )
      logDebug(location, 'Processed virtual tour', { processVirtualTourResult })

      if (processVirtualTourResult) {
        dto = {
          ...dto,
          ...processVirtualTourResult
        }
        logDebug(location, 'Processed virtual tour', { dto })
      }
    }
  }

  if (dto.accessDevices) {
    if (dto.accessDevices.devices?.length) {
      const leafDevice = await linkDevicesInOrder(dto.accessDevices.devices, id)

      dto.accessDevice = {
        ...existingSpace.accessDevice,
        leafNode: leafDevice._id
      }
    }

    if (dto.accessDevices.tourInstructions) {
      dto.accessDevice = {
        ...existingSpace.accessDevice,
        ...(dto.accessDevice || {}),
        instructions: dto.accessDevices.tourInstructions
      }
    }

    Reflect.deleteProperty(dto, 'accessDevices')
  }

  if (dto.rentPrices?.length) {
    dto.displayPrice = findDisplayPrice({
      displayPriceField: community.displayPriceField,
      rentPrices: dto.rentPrices,
      pricesMetadata: dto.pricesMetadata
    })
    logDebug(location, 'Found display price', {
      displayPrice: dto.displayPrice
    })
  }

  dto.building = await setBuilding(
    building,
    dto,
    community,
    existingSpace,
    version
  )
  logDebug(location, 'Building after setBuilding', { building: dto.building })

  if (dto.isVisible === false) {
    dto.sgtEnabled = false
  }

  if (dto.isComplete && scheduledFor) {
    await markScanRequestAsUploaded(id, scheduledFor).catch((err) => {
      logDebug(location, 'Error: completing scan request', { error: err })
      logError(location, 'Error: completing scan request', {
        message: err.message,
        spaceId: id
      })
    })
  }

  const { accessDevice, ...rest } = dto

  logDebug(location, 'Updating space', { id, rest, accessDevice })
  const result = await spaceRepositoryMongoDb.update(
    id,
    {
      ...flattenObjectKeys(rest),
      accessDevice
    } as Space,
    options
  )
  logDebug(location, 'Updated space', { result })

  if (processImage) {
    try {
      await triggerEnhancementPipeline(id)
    } catch (error) {
      logDebug(location, 'Error to trigger enhancement pipeline', { error })
      logError(location, 'Error to trigger enhancement pipeline', {
        error: error.message
      })
    }
  }

  const externalLinks = partialExternalLinks.map((externalLink) => ({
    ...externalLink,
    objectId: id.toString(),
    communityId: community._id.toString(),
    collectionPath: ExternalLinkCollection.Spaces
  }))

  try {
    await createExternalLinks(externalLinks)
    logDebug(location, 'Created external links', { externalLinks })
  } catch (err) {
    logDebug(location, 'Error creating external links', { error: err })
    logError(location, `Error: creating external links for space ${id}`, {
      message: err.message,
      externalLinks
    })
  }

  try {
    logDebug(location, 'Publishing space to the vacancy data topic', {
      dto,
      existingSpace
    })
    await publishSpaceEvent(id, dto, existingSpace, SpaceEventType.UPDATED)
  } catch (error) {
    logDebug(location, 'Error publishing space to the vacancy data topic', {
      error
    })
    logError(location, 'Error publishing space to the vacancy data topic', {
      error: error.message
    })
  }

  if (result?.tourLastUpdatedDate && result?._id) {
    try {
      const scanRequests = await findScanRequestsByQuery({
        'space._id': result._id,
        deletedAt: null
      })
      if (scanRequests.length) {
        await updateScanRequests(
          scanRequests.map((scanRequest) => scanRequest._id.toString()),
          {
            'space.tourLastUpdatedDate': result.tourLastUpdatedDate
          } as Partial<ScanRequest>
        )
      }
    } catch (err) {
      logWarn(location, 'Error updating scan requests', { error: err.message })
    }
  }

  return result
}

async function createExternalLinks(externalLinks?: ExternalLink[]) {
  if (externalLinks?.length > 0) {
    await Promise.all(
      externalLinks.map(async (externalLink) => {
        const params = {
          externalId: externalLink.externalId,
          externalName: externalLink.externalName,
          service: externalLink.service,
          objectId: externalLink.objectId,
          communityId: externalLink.communityId,
          collectionPath: ExternalLinkCollection.Spaces
        }
        return addOrUpdateExternalLinkBy(params, params)
      })
    )
  }
}

export const findUniqueSpace = async (
  communityId: string,
  buildingId: string,
  unit: string
): Promise<Space> => {
  return spaceRepositoryMongoDb.findOne({
    'community._id': communityId,
    'building._id': buildingId,
    unit
  })
}

export const findSpaceByCoordinates = async (
  spaceId: string
): Promise<SpaceFromDistance[]> => {
  const space = await spaceRepositoryMongoDb.findById(spaceId)
  if (!space) {
    logDebug(location, 'Space not found', { spaceId })
    return []
  }

  if (!space.building?.location?.coordinates) {
    logWarn(location, 'Building location not found', { spaceId })
    return []
  }

  const community = await findCommunityById(space.community._id.toString())
  if (!community) {
    logWarn(location, 'Community not found', { spaceId })
    return []
  }

  if (!community.regionalLeasingSettings?.enabled) {
    logDebug(location, 'Regional leasing settings not enabled', { spaceId })
    return []
  }

  const communityIds: Id[] = [new ObjectId(community._id)]
  if (community.regionalLeasingSettings?.enabled) {
    communityIds.push(
      ...community.regionalLeasingSettings.communitiesGroup.map(
        (communityId) => new ObjectId(communityId)
      )
    )
  }

  const query: Query = {
    _id: { $ne: new ObjectId(spaceId) },
    'community._id': { $in: communityIds },
    isVisible: true,
    isComplete: true,
    type: SpaceType.Unit,
    deletedAt: null,
    bedrooms: { $gte: space.bedrooms },
    'building.location': {
      $near: {
        $geometry: {
          type: 'Point',
          coordinates: space.building.location.coordinates
        }
      }
    }
  }

  if (space.displayPrice) {
    const minPrice =
      space.displayPrice -
      space.displayPrice * (community.regionalLeasingSettings.priceRange / 100)
    const maxPrice =
      space.displayPrice +
      space.displayPrice * (community.regionalLeasingSettings.priceRange / 100)

    query['displayPrice'] = { $gte: minPrice, $lte: maxPrice }
  } else if (space.floorPlan?.name) {
    logDebug(location, 'Floor plan name found', {
      floorPlanName: space.floorPlan.name
    })
    query['floorPlan.name'] = space.floorPlan.name
  }

  logDebug(location, 'Query to find spaces', { query })

  let spaces = await spaceRepositoryMongoDb.findAll(query, {
    limit: 50,
    projection:
      '_id bedrooms bathrooms rentPrices.price rentPrices.termInMonths token coverPhoto.url type community._id community.name community.organization._id building.location floorPlan.name displayPrice'
  })

  spaces = await removeSpacesPrices(spaces)

  logDebug(location, 'Found spaces', {
    spaces: spaces.map((space) => String(space._id))
  })

  const spacesWithDistance = spaces.map((foundSpace): SpaceFromDistance => {
    if (
      !space.building?.location?.coordinates ||
      !foundSpace.building?.location?.coordinates
    ) {
      return mapSpaceToSpaceFromDistance(foundSpace, undefined)
    }

    if (
      space.community?._id &&
      foundSpace.community?._id &&
      space.community?._id?.toString() === foundSpace.community?._id?.toString()
    ) {
      return mapSpaceToSpaceFromDistance(foundSpace, 0)
    }

    const distanceFromSimilar = calculateDistance(
      space.building.location.coordinates,
      foundSpace.building.location.coordinates
    )

    return mapSpaceToSpaceFromDistance(foundSpace, distanceFromSimilar)
  })

  const sortedSpaces = spacesWithDistance.sort((a, b) => {
    if (a.distanceFromSimilar === undefined) return 1
    if (b.distanceFromSimilar === undefined) return -1
    return a.distanceFromSimilar - b.distanceFromSimilar
  })

  return sortedSpaces
}

const mapSpaceToSpaceFromDistance = (
  space: Space,
  distanceFromSimilar: number
): SpaceFromDistance => {
  return {
    _id: space._id,
    bedrooms: space.bedrooms,
    bathrooms: space.bathrooms,
    rentPrices: space.rentPrices,
    displayPrice: space.displayPrice,
    token: space.token,
    coverPhoto: {
      url: space.coverPhoto.url
    },
    type: space.type,
    community: {
      _id: space.community?._id?.toString(),
      name: space.community.name
    },
    distanceFromSimilar
  }
}

const calculateDistance = (
  coords1: number[],
  coords2: number[]
): number | undefined => {
  if (!coords1?.[0] || !coords1?.[1] || !coords2?.[0] || !coords2?.[1]) {
    return undefined
  }

  if (!coords1?.[0] || !coords1?.[1] || !coords2?.[0] || !coords2?.[1]) {
    return 0
  }
  const [lon1, lat1] = coords1
  const [lon2, lat2] = coords2
  const R = 3958.8
  const dLat = ((lat2 - lat1) * Math.PI) / 180
  const dLon = ((lon2 - lon1) * Math.PI) / 180
  const a =
    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
    Math.cos((lat1 * Math.PI) / 180) *
      Math.cos((lat2 * Math.PI) / 180) *
      Math.sin(dLon / 2) *
      Math.sin(dLon / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
  return R * c
}
