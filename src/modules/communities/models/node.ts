import { Schema } from 'mongoose'
import { Node } from '../types/node'
import { NodeLink, NodeLinkType } from '../types/nodeLink'
import { infospotSchema } from './infospot'
import { photoSchema } from './photo'

const nodeLinkSchema = new Schema<NodeLink>(
  {
    node: { type: Schema.Types.ObjectId, required: true },
    position: {
      x: { type: Number, required: true },
      y: { type: Number, required: true },
      z: { type: Number, required: true }
    },
    rotation: {
      pitch: { type: Number, required: true },
      yaw: { type: Number, required: true }
    },
    scale: { type: Number },
    type: { type: String, enum: NodeLinkType },
    label: { type: String },
    spaceId: { type: Schema.Types.ObjectId, ref: 'spaces' },
    autoLink: { type: Boolean, default: false },
    visibilityJustification: { type: String }
  },
  { id: false }
)

export const nodeSchema = new Schema<Node>(
  {
    label: { type: String, required: true },
    photo: photoSchema,
    stagedPhoto: photoSchema,
    rotation: {
      pitch: { type: Number, required: true },
      roll: { type: Number, required: true },
      yaw: { type: Number, required: true }
    },
    infospots: [infospotSchema],
    nodeLinks: [nodeLinkSchema],
    nonVisibleNodeLinks: [nodeLinkSchema],
    roomPlan: {
      marker: {
        position: {
          x: { type: Number },
          y: { type: Number },
          z: { type: Number }
        }
      }
    },
    captureCount: { type: Number },
    spaceCategory: { type: String },
    spaceFunction: { type: String },
    spaceDetail: { type: String }
  },
  { timestamps: true, id: false }
)
