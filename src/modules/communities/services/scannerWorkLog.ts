import { getAuthUser } from '@core/auth'
import NotFoundError from '@core/errors/notFoundError'
import { logDebug, logError, logInfo } from '@core/log'
import { KLAVIYO_EVENTS, sendMail } from '@core/mail'
import { PaginatedResult } from '@core/types'
import { getEnvVariable } from '@core/util'
import { findUsers } from '@modules/users/repositories/user'
import { findUserById } from '@modules/users/services/user'
import { User } from '@modules/users/types/user'
import { format } from 'date-fns'
import * as scannerWorkLogRepo from '../repositories/scannerWorkLog'
import {
  findScanRequestsByDateGroupedByScannerAndCommunity,
  findScanRequestsByQuery
} from '../repositories/scanRequest'
import {
  CreateScannerWorkLogDTO,
  FindScannerWorkLogsDTO,
  ScannerWorkLog
} from '../types/scannerWorkLog'
import { ScanRequestStatus } from '../types/scanRequest'
import { findCommunityById } from './community'
import { skipScanRequest } from './scanRequest'

const LOCATION = __filename.substring(__filename.lastIndexOf('/') + 1)

export const createScannerWorkLog = async (
  scannerWorkLogDTO: CreateScannerWorkLogDTO
): Promise<ScannerWorkLog> => {
  const user = getAuthUser()
  if (!user || !user._id) throw new NotFoundError('User not found.')
  const scanner = {
    _id: user._id,
    name: user.name,
    email: user.email
  }
  const community = await findCommunityById(scannerWorkLogDTO.community._id)
  if (!community) throw new NotFoundError('Community not found.')

  const workLogDate = new Date(scannerWorkLogDTO.workLogDate)
  workLogDate.setHours(0, 0, 0, 0)

  const existingWorkLog =
    await scannerWorkLogRepo.findScannerWorkLogByDateScannerAndCommunity(
      workLogDate,
      scanner._id.toString(),
      community._id.toString()
    )
  if (existingWorkLog) {
    const updatedExistingWorkLog = scannerWorkLogRepo.updateScannerWorkLog(
      { _id: existingWorkLog._id },
      {
        commuteTime: scannerWorkLogDTO.commuteTime,
        workingHours: scannerWorkLogDTO.workingHours
      }
    )
    return updatedExistingWorkLog
  }

  const scannerWorkLog: ScannerWorkLog = {
    scanner,
    spaces: [],
    scanRequests: [],
    community,
    workingHours: scannerWorkLogDTO.workingHours,
    commuteTime: scannerWorkLogDTO.commuteTime,
    workLogDate
  }
  const result = await scannerWorkLogRepo.createScannerWorkLog(scannerWorkLog)
  return result
}

export const findScannerWorkLogs = async (
  scannerWorkLogDTO: FindScannerWorkLogsDTO
): Promise<PaginatedResult<ScannerWorkLog>> => {
  const result = await scannerWorkLogRepo.findScannerWorkLogs(scannerWorkLogDTO)
  return result
}

export const updateScannerWorkLogById = async (
  scannerWorkLogId: string,
  scannerWorkLogDTO: Partial<ScannerWorkLog>
): Promise<ScannerWorkLog> => {
  if (scannerWorkLogDTO.workLogDate) {
    const workLogDate = new Date(scannerWorkLogDTO.workLogDate)
    workLogDate.setHours(0, 0, 0, 0)
    scannerWorkLogDTO.workLogDate = workLogDate
  }
  const result = await scannerWorkLogRepo.updateScannerWorkLog(
    { _id: scannerWorkLogId },
    scannerWorkLogDTO
  )
  return result
}

export const deleteScannerWorkLogById = async (
  scannerWorkLogId: string
): Promise<ScannerWorkLog> => {
  const result = await scannerWorkLogRepo.deleteScannerWorkLog({
    _id: scannerWorkLogId
  })
  return result
}

export const consolidateScannerWorkLogs = async (startDate: Date) => {
  logInfo(LOCATION, `📅 Start date: ${startDate}`)
  const endDate = new Date(startDate)
  endDate.setHours(23, 59, 59, 999)
  logInfo(LOCATION, `📅 End date: ${endDate}`)

  logInfo(LOCATION, '🔍 Finding scan requests scheduled for today.')
  const scheduledForToday = await findScanRequestsByQuery({
    status: ScanRequestStatus.SCHEDULED,
    scheduledFor: {
      $gte: startDate,
      $lte: endDate
    }
  })
  logInfo(LOCATION, `🔍 Found ${scheduledForToday.length} scan requests.`)

  const workLogJanitorId = getEnvVariable('WORKLOG_JANITOR_ID')
  const workLogJanitor = await findUserById(workLogJanitorId)
  if (!workLogJanitor) throw new NotFoundError('Admin user not found.')

  logDebug(LOCATION, 'Work log janitor', workLogJanitor)

  if (scheduledForToday.length) {
    logInfo(LOCATION, '🚫 Skipping scan requests scheduled for today.')
    const updates = await Promise.all(
      scheduledForToday.map((scanRequest) => {
        return skipScanRequest(
          scanRequest._id.toString(),
          'Peek Skipped',
          workLogJanitor
        )
      })
    )
    logDebug(LOCATION, 'Updates', updates)
    logInfo(LOCATION, `🚫 Skipped ${updates.length} scan requests.`)
  }

  logInfo(
    LOCATION,
    `🔍 Getting COMPLETED and SKIPPED scan requests scheduled for ${startDate} by community and scanner`
  )
  const groupedRequests =
    await findScanRequestsByDateGroupedByScannerAndCommunity(startDate, endDate)
  logInfo(
    LOCATION,
    `🔍 Found ${groupedRequests.length} groups of scan requests.`
  )

  const workLogs = await Promise.all(
    groupedRequests.map(async (group) => {
      const scanner =
        group.scanRequests[0]?.scannedBy ||
        group.scanRequests[0]?.skippedBy ||
        workLogJanitor
      logInfo(
        LOCATION,
        `🔍 Creating scanner work log for scanner ${scanner._id.toString()} in community ${group._id.communityId.toString()}.`
      )

      const workLogDate = startDate

      logInfo(
        LOCATION,
        `🔍 Checking if scanner work log exists for scanner ${scanner._id.toString()} in community ${group._id.communityId.toString()}.`
      )
      const existingWorkLog =
        await scannerWorkLogRepo.findScannerWorkLogByDateScannerAndCommunity(
          workLogDate,
          scanner._id.toString(),
          group._id.communityId.toString()
        )
      if (existingWorkLog) {
        logInfo(
          LOCATION,
          `🔍 Existing scanner work log found.${existingWorkLog._id.toString()}`
        )
        logInfo(
          LOCATION,
          `🔍 Updating existing scanner work log for scanner ${scanner._id.toString()} in community ${group._id.communityId.toString()}.`
        )
        const updatedExistingWorkLog = scannerWorkLogRepo.updateScannerWorkLog(
          { _id: existingWorkLog._id.toString() },
          {
            scanRequests: group.scanRequests.map((request) =>
              request._id.toString()
            ),
            spaces: group.scanRequests.map((request) =>
              request.space._id.toString()
            )
          }
        )
        return updatedExistingWorkLog
      }

      logInfo(LOCATION, '🔍 Existing scanner work log NOT found.')

      const scannerWorkLog: ScannerWorkLog = {
        scanner: {
          _id: scanner._id,
          name: scanner.name,
          email: scanner.email
        },
        spaces: group.scanRequests.map((request) =>
          request.space._id.toString()
        ),
        scanRequests: group.scanRequests.map((request) =>
          request._id.toString()
        ),
        community: {
          _id: group.scanRequests[0].community._id,
          name: group.scanRequests[0].community.name
        },
        workingHours: 0,
        commuteTime: 0,
        workLogDate
      }
      logInfo(
        LOCATION,
        `🔍 Creating scanner work log for scanner ${scanner._id.toString()} in community ${group._id.communityId.toString()}.`
      )
      const result = scannerWorkLogRepo.createScannerWorkLog(scannerWorkLog)
      return result
    })
  )

  logInfo(LOCATION, `🔍 Consolidation complete. ${workLogs.length} affected.`)
}

export const sendWorkLogRecapEmail = async (workLogDate: Date) => {
  logInfo(LOCATION, '🔍 Sending work log recap email.')

  const workLogs = await scannerWorkLogRepo.findScannerWorkLogsByQuery(
    {
      workLogDate
    },
    ['spaces', 'scanRequests']
  )

  if (!workLogs.length) {
    logInfo(LOCATION, '🔍 No work logs found.')
    return
  }

  logInfo(LOCATION, `🔍 Found ${workLogs.length} work logs.`)
  const workLogIdsSent: string[] = []
  const allSendMailPromises: Promise<{
    status: string
    message: string
  }>[] = []

  for (const workLog of workLogs) {
    logInfo(LOCATION, '🔍 Preparing work log recap.', {
      workLogId: workLog._id.toString(),
      workLogDate: workLog.workLogDate,
      community: workLog.community._id.toString(),
      workLogScanner: workLog.scanner._id.toString()
    })

    const users: User[] = await findUsers({
      'communities._id': workLog.community._id.toString(),
      receiveScansSessionsReportEmail: true,
      status: 'active'
    })

    if (!users.length) {
      logInfo(LOCATION, '🔍 No users found for this work log.', {
        workLogId: workLog._id.toString()
      })
      continue
    }

    const skippedRequests = workLog.scanRequests.filter(
      (request) => request.status === ScanRequestStatus.SKIPPED
    )
    const completedRequests = workLog.scanRequests.filter(
      (request) => request.status === ScanRequestStatus.COMPLETED
    )

    const completedSpaces = workLog.spaces.filter((space) =>
      completedRequests.some(
        (request) => request.space._id.toString() === space._id.toString()
      )
    )

    const skippedSpaces = workLog.spaces.filter((space) =>
      skippedRequests.some(
        (request) => request.space._id.toString() === space._id.toString()
      )
    )

    const capturedSpacesList = completedSpaces.map((space) => ({
      name: space.unit,
      floorPlan: space?.floorPlan?.name ?? '',
      tourLink: `${getEnvVariable('WEB_VIEWER_BASE_URL')}?token=${
        space.token
      }&pageType=unit`
    }))

    const skippedSpacesList = skippedSpaces.map((space) => ({
      name: space.unit,
      skippedReason: skippedRequests.find(
        (request) => request.space._id.toString() === space._id.toString()
      )?.reason
    }))

    if (!completedSpaces.length && !skippedSpaces.length) {
      logInfo(
        LOCATION,
        '🔍 No captured or skipped spaces found for this work log.',
        { workLogId: workLog._id.toString() }
      )
      continue
    }

    logInfo(
      LOCATION,
      `🔍 Found ${completedSpaces.length} captured spaces and ${
        skippedSpaces.length
      } skipped spaces for work log ${workLog._id.toString()}.`
    )
    logDebug(
      LOCATION,
      `🔍 Captured spaces: ${JSON.stringify(capturedSpacesList)}
        Skipped spaces: ${JSON.stringify(skippedSpacesList)}`
    )

    const commaSeparatedSkippedSpacesIdsList = skippedRequests
      .map((request) => request.space._id.toString())
      .join(',')

    const newPhotoshootRequestLink = `${getEnvVariable(
      'AGENT_DASHBOARD_WEB_URL'
    )}/scan-requests/add-new-request?community=${workLog.community._id.toString()}&spaces=${commaSeparatedSkippedSpacesIdsList}`

    const sendMailRequests = users.map((user) => {
      logInfo(
        LOCATION,
        `🔍 Queuing work log recap email for user ${user._id.toString()} (${
          user.email
        }), work log ${workLog._id.toString()}.`
      )
      if (!workLogIdsSent.includes(workLog._id.toString())) {
        workLogIdsSent.push(workLog._id.toString())
      }
      return sendMail(user.email, KLAVIYO_EVENTS.SCANS_SESSIONS_REPORT, {
        userName: user.name,
        capturedSpacesList,
        skippedSpacesList,
        communityName: workLog.community.name,
        newPhotoshootRequestLink,
        photoshootDate: format(workLog.workLogDate, 'MMM d, yyyy')
      })
    })

    allSendMailPromises.push(...sendMailRequests)
  }

  const results = await Promise.allSettled(allSendMailPromises)

  logInfo(LOCATION, '🔍 All work log recap emails processed.', {
    totalEmailsAttempted: allSendMailPromises.length,
    successfulEmails: results.filter((r) => r.status === 'fulfilled').length,
    failedEmails: results.filter((r) => r.status === 'rejected').length
  })

  results
    .filter((r) => r.status === 'rejected')
    .forEach((rejection, index) => {
      logError(
        LOCATION,
        'Failed to send work log recap email: worklog:' + workLogIdsSent[index],
        (rejection as PromiseRejectedResult).reason
      )
      throw new Error(
        'Failed to send work log recap email: worklog:' + workLogIdsSent[index]
      )
    })

  if (!workLogIdsSent.length) {
    logInfo(LOCATION, '🔍 No work logs found.')
    return
  }

  try {
    await Promise.all(
      workLogIdsSent.map((id) =>
        scannerWorkLogRepo.updateScannerWorkLog(
          { _id: id },
          { photoshootReportSent: true }
        )
      )
    )
    logInfo(LOCATION, 'Updated work logs with photoshootReportSent flag.', {
      workLogIds: workLogIdsSent
    })
  } catch (error) {
    logError(LOCATION, 'Failed to update work logs', {
      workLogIds: workLogIdsSent,
      error
    })
  }
}
