import { Organization } from '@modules/users/types/organization'
import { ToObjectOptions } from 'mongoose'
import { Address } from './address'
import { Building } from './building'
import { Community } from './community'
import { ExternalLink } from './externalLink'
import { Id } from './id'
import { Node } from './node'
import { SpaceType } from './spaceType'

export const priceFields = [
  'salePrice',
  'rentPrices',
  'grossRent',
  'displayPrice',
  'pricesMetadata',
  'dxUpdate.data.rentPrices',
  'dxUpdate.data.pricesMetadata'
]

export enum RentCafeV2VirtualTourSent {
  Published = 'published',
  Unpublished = 'unpublished'
}

export interface Space {
  _id: Id | string
  community: SpaceCommunity
  nodes: Node[]
  building: SpaceBuilding
  availableDate?: Date
  readyToRentDate?: Date
  type: SpaceType
  startNode?: Id
  unit?: string
  bedrooms?: number
  bathrooms?: number
  salePrice?: number
  rentPrices?: RentPrice[]
  currency?: string
  isMultiRes: boolean
  isHorizonLevel: boolean
  isForSale?: boolean
  isVisible?: boolean
  deletedAt: Date | null
  amenities?: string[]
  isComplete: boolean
  createdBy?: Id
  scanRequested?: boolean
  grossRent?: number
  description?: string
  floorPlan?: FloorPlan
  coverPhoto?: ImageUpload
  showContactForm?: boolean
  canBypassContactForm?: boolean
  isModelUnit?: boolean
  publishGmbStatus?: string
  gmbImageCheckups?: Array<Date>
  availabilityStatus?: string
  gmbUser?: Id | string
  unitSize?: number
  externalSpaceStatus?: ExternalSpaceStatus[]
  sgtEnabled: boolean
  token?: string
  publishApartmentsDotCom?: boolean
  createdAt?: string
  tourCapturedDate?: Date
  tourLastUpdatedDate?: Date
  dxUpdate?: SpaceDxUpdate
  accessDevice?: {
    leafNode: Id | string
    instructions?: string
  }
  pricesMetadata?: PricesMetadata
  displayPrice: number
  address: Address
  isMarketable?: boolean
  updatedBy: string
  availableDatesMetadata?: AvailableDatesMetadata
  availableStatusesMetadata?: AvailableStatusesMetadata
  category?: string
  rentCafeV2VirtualTourSent?: RentCafeV2VirtualTourSent
  spaceCategory?: string
  spaceFunction?: string
  spaceDetail?: string
  communityFull?: Community
}

export type SpaceQuery = any & {
  offset?: number
  limit?: number
}

export type QueryOptions = {
  skipAclScope?: boolean
  includeSoftDeleted?: boolean
  includeDeactivated?: boolean
}

export interface SpaceBuilding {
  _id: Id | string
  name?: string
  address: Address
  alternativeName?: string
  communityId?: Id | string
  location?: {
    type: string
    coordinates: number[]
  }
}

export interface SpaceDxUpdate {
  started: Date
  finished?: Date
  errorMessage?: string
  data?: SpaceDxUpdateData
}

export interface SpaceDxUpdateData {
  unit: string
  bedrooms: number
  bathrooms: number
  rentPrices: RentPrice[]
  floorPlan: FloorPlan
  availabilityStatus: string
  pricesMetadata?: PricesMetadata
  isVisible?: boolean
  availableDatesMetadata?: AvailableDatesMetadata
  availableStatusesMetadata?: AvailableStatusesMetadata
}

export interface SpaceCommunity {
  _id: Id | string
  organization: Pick<Organization, '_id' | 'name'>
  name: string
  logo?: string
  primaryColor?: string
  applyUrl?: string
  applyText?: string
  gaTrackingId?: string
  customJs?: string
  secondaryColor?: string
  showContactForm?: boolean
  canBypassContactForm?: boolean
  autoDisplayContactFormNavigationCount?: number
  displayBuildingName?: boolean
  displayPriceField: string
  isActive?: boolean
  metroArea?: string
  showAddress?: boolean
}

export interface RentPrice {
  termInMonths: number
  price: number
  enabled: boolean
}

export enum ExternalSpaceStatus {
  Ready = 'Ready',
  Unrented = 'Unrented',
  Vacant = 'Vacant',
  Available = 'Available',
  Unavailable = 'Unavailable'
}

export interface UnitSpace extends Space {
  unit: string
  bedrooms: number
  bathrooms: number
  building: SpaceBuilding
  salePrice: number
  rentPrices: RentPrice[]
  isVisible: boolean
  description: string
  scanRequested: boolean
  currency: string
  isForSale: boolean
  amenities: string[]
  grossRent: number
  floorPlan: FloorPlan
  coverPhoto: ImageUpload
  showContactForm: boolean
  canBypassContactForm: boolean
  isModelUnit: boolean
  unitSize: number
  externalSpaceStatus: ExternalSpaceStatus[]
}

export interface ImageUpload {
  url?: string
  urlChangedAt?: Date
}

export interface RoomPlanDimension {
  height: number
  width: number
  length: number
}

export interface FloorPlan extends ImageUpload {
  externalId?: string
  name: string
  roomPlanFile?: string
  roomPlanDimension?: RoomPlanDimension
}

export type SpaceWithExternalLinks = Space & {
  externalLinks: ExternalLink[]
}

export interface ProcessImage {
  processImage?: boolean
}

export type UpdateSpaceByIdQuery = ProcessImage
export type AddOrUpdateNodeQuery = ProcessImage

export interface MultiresPayload {
  bucket: string
  inputKey: string
  outputKey: string
}

export type PricesMetadata = {
  [key: string]: number
}

export type AvailableDatesMetadata = {
  [key: string]: Date
}

export type AvailableStatusesMetadata = {
  [key: string]: any
}

export const SPACE_EXTERNAL_AVAILABILITY_REGEX =
  /\b(Ready|Unrented|Vacant|Available|true)\b/i

export type MoveSpaceParams = {
  spaceId: string
  communityId: string
  desiredCommunityId: string
  desiredBuildingId: string
}

export type MoveSpacesEvent = {
  items: MoveSpaceParams[]
}

export type MoveSpacesFetchedItem = {
  space: Space | null
  community: Community | null
  desiredCommunity: Community | null
  desiredBuilding: Building | null
}

export type MoveSpacesResult = {
  spaceId: string
  spaceUnit: string
  communityId: string
  communityName: string
  desiredCommunityId: string
  desiredCommunityName: string
  desiredBuildingId: string
  desiredBuildingName: string
  textResponse: string
}

export interface CoupleSpaceIds {
  scannerId: string
  syncedId: string
  version?: string | undefined
}

export interface CoupleEventParams {
  couples: CoupleSpaceIds[]
  readOnly: boolean
}

export interface CoupleResponse {
  success: boolean
  message: string
}

export type RemovePrice = {
  result: Space
  community?: Community
}

export type SpaceRemovePrices = Space & {
  toObject?: (params?: ToObjectOptions) => Space
}

export type GetAutoScanFieldsByCommunity = {
  dateFields: string[]
  statusFields: {
    field: string
    values: string[]
  }[]
}
