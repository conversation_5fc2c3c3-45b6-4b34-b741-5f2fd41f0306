import { Id } from './id'
import { Position } from './position'

export enum NodeLinkType {
  Node = 'node',
  Space = 'space'
}

export interface NodeLink {
  node: Id
  label: string
  position: Position
  type: NodeLinkType
  rotation: {
    pitch: number
    yaw: number
  }
  scale?: number
  spaceId?: Id // only if NodeLinkType is Space
  _id?: Id
  autoLink?: boolean
  visibilityJustification?: string
}
