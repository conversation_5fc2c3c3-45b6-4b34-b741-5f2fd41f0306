import { CrudRepositoryMongoDb } from '@core/repositories/CrudRepository'
import { crudRepositoryMongoDbFactory } from '@core/repositories/crudRepositoryFactory'
import { CommunityModel } from '@modules/communities/models/community'
import { Community } from '@modules/communities/types/community'

export type CommunityRepository = CrudRepositoryMongoDb<Community>

export const communityRepositoryMongoDb: CommunityRepository = {
  ...crudRepositoryMongoDbFactory(CommunityModel)
}
