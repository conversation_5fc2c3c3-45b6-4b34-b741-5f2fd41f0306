import { MONGO_ERROR_CODES } from '@core/errors/mongoErrorCodes'
import { logWarn } from '@core/log'
import {
  escapeRegex,
  fieldsToProjection,
  flattenObjectKeys,
  ProjectionFields
} from '@core/util'
import { CompatibleListingUnit } from '@modules/communities/services/facade/getCompatibleListingUnits'
import { SpaceType } from '@modules/communities/types/spaceType'
import { Availability } from '@modules/partnersApi/types'
import mongoose, { FilterQuery, isValidObjectId, UpdateQuery } from 'mongoose'
import { ExternalLinkModel } from '../models/externalLink'
import { ListingLinkModel } from '../models/listingLink'
import { getFullUrl } from '../models/photo'
import { SpaceModel } from '../models/space'
import {
  addSpaceIdInBuilding,
  findBuildingById,
  removeSpaceIdFromBuilding
} from '../repositories/building'
import { logoUrl } from '../services/community'
import { generatePopulate } from '../services/space'
import { Building } from '../types/building'
import {
  ExternalLink,
  ExternalLinkCollection,
  ExternalLinkService
} from '../types/externalLink'
import { ObjectId } from '../types/id'
import {
  QueryOptions,
  Space,
  SpaceBuilding,
  SpaceCommunity,
  SpaceDxUpdate,
  SpaceQuery,
  SpaceWithExternalLinks
} from '../types/space'
import { addOrUpdateExternalLinkBy } from './externalLink'

const location = 'repositories/space'

export const findSpaceById = async (
  id: string,
  fields?: string,
  options?: QueryOptions
): Promise<Space | null> => {
  if (!isValidObjectId(id)) {
    logWarn(location, 'Invalid Space Id when getting space', { id })
    return null
  }
  return SpaceModel.findById(id).select(fields).setOptions(options)
}

export const findSpaceByIdLean = async (
  id: string,
  fields?: string
): Promise<Space | null> => SpaceModel.findById(id).select(fields).lean()

/** @deprecated use spacex service instead */
export const addSpace = async (space: Partial<Space>): Promise<Space> => {
  const newSpace = (await SpaceModel.create(space)).toObject()
  await addSpaceIdInBuilding(newSpace.building._id, newSpace._id)
  return newSpace
}

export const findUniqueSpace = async (
  unit: string,
  type: string,
  buildingId: string,
  communityId: string
): Promise<Space | null> => {
  return SpaceModel.findOne({
    unit,
    type,
    'building._id': buildingId,
    'community._id': communityId
  }).lean()
}

export const updateSpacesDxUpdate = async (
  communityId: string,
  data: Partial<SpaceDxUpdate>
) => {
  await SpaceModel.updateMany(
    { 'community._id': communityId, deletedAt: { $eq: null } },
    { $set: { dxUpdate: data } }
  )
}

export const updateSpaceDxUpdate = async (
  spaceId: string,
  data: Partial<SpaceDxUpdate>
) => {
  await SpaceModel.updateOne({ _id: spaceId }, { $set: { dxUpdate: data } })
}

/** @deprecated use spacex service instead */
export const addOrUpdateSpaceById = async (
  id: string,
  data: Partial<Space>,
  externalLinks?: ExternalLink[],
  building?: Building
): Promise<Space> => {
  let spaceBuilding = data.building || building

  if (id && spaceBuilding?._id) {
    const existingSpace = await findSpaceById(id, 'building')
    if (
      existingSpace?.building?._id &&
      existingSpace.building._id.toString() !== spaceBuilding._id.toString()
    ) {
      await removeSpaceIdFromBuilding(existingSpace.building._id, id)
    }

    spaceBuilding = await findBuildingById(spaceBuilding._id.toString())
    if (spaceBuilding === null) {
      spaceBuilding = {} as SpaceBuilding
    }
  }

  // TODO: validate this change
  const flattened = flattenObjectKeys({
    ...data,
    building: spaceBuilding
  })

  const newSpace = await SpaceModel.findOneAndUpdate(
    { _id: new ObjectId(id) || new ObjectId() },
    { $set: flattened },
    { upsert: true, new: true }
  )

  await addSpaceIdInBuilding(newSpace.building._id, newSpace._id)

  if (externalLinks?.length > 0) {
    await Promise.all(
      externalLinks.map(async (externalLink) => {
        const params = {
          externalId: externalLink.externalId,
          externalName: externalLink.externalName,
          service: externalLink.service,
          objectId: newSpace._id.toString(),
          communityId: newSpace.community._id.toString(),
          collectionPath: ExternalLinkCollection.Spaces
        }
        return addOrUpdateExternalLinkBy(params, params)
      })
    )
  }

  return newSpace
}

export const addOrUpdateSpaceByQuery = async (
  query: SpaceQuery,
  data: Partial<Space>
): Promise<Space> => {
  return SpaceModel.findOneAndUpdate(
    query,
    { $set: data },
    { upsert: true, new: true }
  )
}

export const updateSpaceByOrganizationId = async (
  id: string,
  data: Partial<SpaceCommunity>
): Promise<any> => {
  return SpaceModel.updateMany(
    { 'community.organization._id': id },
    { $set: { 'community.organization': data } }
  )
}

export const findSpaces = async (
  query: any,
  projection?: string,
  populate?: string
): Promise<Space[]> => {
  const {
    search,
    offset = 0,
    limit = 5000,
    orderBy,
    order,
    linkedToAccessDevice,
    ...queryObj
  } = query
  const filterQuery: FilterQuery<Space> = queryObj

  if (search) {
    filterQuery.$or = [
      { unit: { $regex: escapeRegex(search), $options: 'i' } },
      {
        'building.address.street': {
          $regex: escapeRegex(search),
          $options: 'i'
        }
      },
      { label: { $regex: escapeRegex(search), $options: 'i' } },
      { 'floorPlan.name': { $regex: escapeRegex(search), $options: 'i' } }
    ]
  }

  if (linkedToAccessDevice === 'true') {
    filterQuery['accessDevice'] = { $exists: true, $ne: null }
  }
  if (linkedToAccessDevice === 'false') {
    const accessDeviceCondition = [
      { accessDevice: { $exists: false } },
      { accessDevice: null }
    ]

    if (filterQuery.$or) {
      filterQuery.$or.push(...accessDeviceCondition)
    } else {
      filterQuery.$or = accessDeviceCondition
    }
  }

  const populateObj = generatePopulate(populate)

  const spaces = await SpaceModel.find(queryObj)
    .populate(populateObj)
    .select(projection && fieldsToProjection(projection, ','))
    .limit(limit)
    .skip(offset)
    .sort({ [orderBy || 'createdAt']: order || 'desc' })
    .lean({ virtuals: true, getters: true })

  const updatedSpaces = spaces.map(updateSpaceUrls)

  return updatedSpaces
}

export const updateSpaceUrls = (space: Space) => {
  if (space.community) {
    space.community.logo = space.community.logo
      ? logoUrl(space.community.logo)
      : null
  }

  if (space.coverPhoto && space.coverPhoto.url) {
    space.coverPhoto.url = space.coverPhoto.url
      ? getFullUrl(space.coverPhoto.url)
      : null
  }

  if (space.nodes && Array.isArray(space.nodes)) {
    space.nodes = space.nodes.map((node) => {
      if (node.stagedPhoto) {
        node.stagedPhoto.url = node.stagedPhoto.url
          ? getFullUrl(node.stagedPhoto.url)
          : null
        node.stagedPhoto.lowResUrl = node.stagedPhoto.lowResUrl
          ? getFullUrl(node.stagedPhoto.lowResUrl)
          : null
        node.stagedPhoto.multiResConfigUrl = node.stagedPhoto.multiResConfigUrl
          ? getFullUrl(node.stagedPhoto.multiResConfigUrl)
          : null
      }

      if (node.photo) {
        node.photo.url = node.photo.url ? getFullUrl(node.photo.url) : null
        node.photo.lowResUrl = node.photo.lowResUrl
          ? getFullUrl(node.photo.lowResUrl)
          : null
        node.photo.multiResConfigUrl = node.photo.multiResConfigUrl
          ? getFullUrl(node.photo.multiResConfigUrl)
          : null
      }

      return node
    })
  }
  return space
}

export const countSpaces = async (query: SpaceQuery): Promise<number> => {
  const { name, linkedToAccessDevice, ...q } = query

  Reflect.deleteProperty(q, 'offset')
  Reflect.deleteProperty(q, 'limit')
  Reflect.deleteProperty(q, 'orderBy')
  Reflect.deleteProperty(q, 'order')

  const queryObj: FilterQuery<Space> = q
  if (name) {
    queryObj.name = { $regex: escapeRegex(name), $options: 'i' }
  }

  if (linkedToAccessDevice === 'true') {
    queryObj['accessDevice'] = { $exists: true, $ne: null }
  }
  if (linkedToAccessDevice === 'false') {
    const accessDeviceCondition = [
      { accessDevice: { $exists: false } },
      { accessDevice: null }
    ]

    if (queryObj.$or) {
      queryObj.$or.push(...accessDeviceCondition)
    } else {
      queryObj.$or = accessDeviceCondition
    }
  }

  const totalCount = await SpaceModel.countDocuments(queryObj)
  return totalCount
}

export const getSpaceByToken = async (token: string): Promise<Space | null> => {
  return SpaceModel.findOne({ token })
}

export const getSpaceByTokenLegacy = async (token: string): Promise<any> => {
  if (!mongoose.isValidObjectId(token)) return null

  const link = await ListingLinkModel.findOne({
    _id: new ObjectId(token)
  })

  if (!link) return null

  return SpaceModel.findOne({ _id: link?.listing_id })
}

export const findSpacesWithExternalLinksByCommunityId = async (
  communityId: string,
  organizationId: string,
  availability?: Availability,
  type?: SpaceType,
  options?: {
    offset?: number
    limit?: number
    fields?: string | ProjectionFields
  }
): Promise<Partial<SpaceWithExternalLinks[]>> => {
  const { offset, limit, fields } = options || {}

  let project = null
  if (fields) {
    project = {
      $project: {
        ...(fieldsToProjection(fields, ' ') ?? {})
      }
    }
  }

  let availabilityQuery = {}

  if (availability === Availability.Available) {
    availabilityQuery = {
      availableDate: { $lte: new Date() }
    }
  } else if (availability === Availability.Unavailable) {
    availabilityQuery = {
      availableDate: { $gt: new Date() }
    }
  }

  const matchByFilters = {
    $match: {
      'community._id': new ObjectId(communityId),
      'community.organization._id': new ObjectId(organizationId),
      deletedAt: null,
      ...availabilityQuery,
      ...(type && { type: type })
    }
  }

  const lookupExternalLinks = {
    $lookup: {
      from: 'externallinks',
      localField: '_id',
      foreignField: 'objectId',
      as: 'externalLinks'
    }
  }

  const lookupBuildingExternalLinks = {
    $lookup: {
      from: 'externallinks',
      localField: 'building._id',
      foreignField: 'objectId',
      as: 'building.externalLinks'
    }
  }

  const query = [
    matchByFilters,
    project,
    lookupExternalLinks,
    lookupBuildingExternalLinks
  ]

  if (offset) {
    query.push({ $skip: offset })
  }
  if (limit) {
    query.push({ $limit: limit > 10 ? 10 : limit })
  }

  return SpaceModel.aggregate(query)
}

export const findSpacesByPublishApartmentsDotComCursor = async () => {
  return SpaceModel.find({
    publishApartmentsDotCom: true,
    type: SpaceType.Unit
  }).cursor()
}

export const findSpacesByIds = async (spaceIds: string[]) => {
  return SpaceModel.find({
    _id: { $in: spaceIds }
  }).lean()
}
export type FindSpaceByExternalIdInput = {
  externalId: string
  service: ExternalLinkService
  organizationId?: string
  fields?: string
}
export const findSpaceByExternalId = async ({
  externalId,
  service,
  organizationId,
  fields
}: FindSpaceByExternalIdInput): Promise<Space | null> => {
  let project = null

  if (fields) {
    const fieldsObj = fieldsToProjection(fields, ' ')
    project = {
      $project: {
        ...fieldsObj
      }
    }
  }

  const query = []
  query.push(
    {
      $match: {
        externalId,
        service,
        collectionPath: ExternalLinkCollection.Spaces,
        deletedAt: null
      }
    },
    {
      $lookup: {
        from: 'spaces',
        localField: 'objectId',
        foreignField: '_id',
        as: 'space'
      }
    }
  )

  if (organizationId) {
    query.push({
      $match: {
        'space.community.organization._id': new ObjectId(organizationId)
      }
    })
  }
  query.push(
    {
      $project: {
        space: {
          $arrayElemAt: ['$space', 0]
        }
      }
    },
    {
      $replaceRoot: {
        newRoot: '$space'
      }
    }
  )

  if (project) query.push(project)

  const spaces = await ExternalLinkModel.aggregate(query)
  return spaces[0] || null
}

export const findSpacesByCommunityExternalServiceId = async (
  externalId: string,
  organizationId: string,
  service: string,
  availability?: Availability,
  type?: SpaceType,
  options?: {
    offset?: number
    limit?: number
    fields?: string | ProjectionFields
  }
): Promise<Partial<Space>[]> => {
  const { offset, limit, fields } = options || {}

  let project = null

  if (fields) {
    project = {
      $project: {
        ...(fieldsToProjection(fields, ' ') || {})
      }
    }
  }
  const query = [
    {
      $match: {
        externalId,
        service,
        collectionPath: 'communities'
      }
    },
    {
      $lookup: {
        from: 'communities',
        localField: 'objectId',
        foreignField: '_id',
        as: 'communities'
      }
    },
    {
      $match: {
        'communities.organization._id': new ObjectId(organizationId)
      }
    },
    {
      $project: {
        community: {
          $arrayElemAt: ['$communities', 0]
        }
      }
    },
    {
      $lookup: {
        from: 'spaces',
        let: { communityId: '$community._id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ['$community._id', '$$communityId']
              }
            }
          },
          {
            $match: {
              deletedAt: null
            }
          }
        ],
        as: 'spaces'
      }
    },
    {
      $unwind: {
        path: '$spaces',
        preserveNullAndEmptyArrays: true
      }
    },
    {
      $replaceRoot: {
        newRoot: '$spaces'
      }
    },
    {
      $match: {
        ...(availability === Availability.Available && {
          availableDate: { $lte: new Date() }
        }),
        ...(availability === Availability.Unavailable && {
          availableDate: { $gt: new Date() }
        }),
        ...(type && { type: type })
      }
    },
    project,
    {
      $lookup: {
        from: 'externallinks',
        localField: '_id',
        foreignField: 'objectId',
        as: 'externalLinks'
      }
    },
    {
      $lookup: {
        from: 'externallinks',
        localField: 'building._id',
        foreignField: 'objectId',
        as: 'building.externalLinks'
      }
    }
  ]

  if (offset) {
    query.push({ $skip: offset })
  }
  if (limit) {
    query.push({ $limit: limit > 10 ? 10 : limit })
  }

  const aggregate = await ExternalLinkModel.aggregate(query)

  return aggregate || []
}

export function findSpacesByCommunityIdCursor(communityId: string) {
  return SpaceModel.find(
    {
      'community._id': communityId,
      isComplete: true,
      deletedAt: null
    },
    {
      _id: 1,
      community: 1,
      floorPlan: 1,
      unit: 1,
      type: 1,
      isModelUnit: 1,
      createdAt: 1,
      coverPhoto: 1
    }
  ).cursor()
}

export const removeSpaceById = async (id: string): Promise<Space> => {
  return SpaceModel.findByIdAndUpdate(id, {
    $set: { deletedAt: new Date() }
  })
}

export const updateSpaceCommunityById = async (
  id: string,
  data: Partial<SpaceCommunity> | UpdateQuery<typeof SpaceModel>
) => {
  const space = await SpaceModel.updateMany(
    { 'community._id': new ObjectId(id) },
    { $set: { community: data } }
  )

  return space
}

export const findAllSpacesByCommunityIdPopulate = async (
  communityId: string
) => {
  return SpaceModel.find({
    'community._id': communityId
  })
    .populate('community')
    .populate('building')
    .lean()
}

export const findAllCompletedSpacesByCommunityIdPopulate = async (
  communityId: string,
  populate: string[]
) => {
  return SpaceModel.find({
    'community._id': communityId,
    isComplete: true,
    type: SpaceType.Unit
  })
    .populate(populate)
    .lean()
}

export const findAllSpacesByCommunityIdAndSpaceTypePopulate = async (
  spaceType: SpaceType,
  communityId?: string,
  showAllVisible = false,
  bedrooms?: string[],
  buildingId?: string,
  version?: string
) => {
  let query: any = {
    type: spaceType,
    isComplete: true
  }

  if (spaceType === SpaceType.Unit && !showAllVisible) {
    query = {
      ...query,
      isVisible: true
    }
  }

  if (communityId) {
    query['community._id'] = communityId
  } else if (buildingId && version !== '1') {
    query['building._id'] = buildingId
  }

  if (bedrooms !== undefined && bedrooms.length > 0) {
    query['$or'] = []
    for (const bedroom of bedrooms) {
      query['$or'].push({ bedrooms: parseInt(bedroom) })
    }
  }

  if (version === '1') {
    return SpaceModel.find(query)
      .populate('community')
      .sort({ bedrooms: 1 })
      .lean()
  }

  return SpaceModel.find(query)
    .populate('community')
    .populate('building')
    .sort({ bedrooms: 1 })
    .lean()
}

export const findAllSpacesByCompatibleListing = async ({
  communityIds,
  buildingIds,
  floorplanNames,
  units,
  version
}: CompatibleListingUnit) => {
  const query: any = { $and: [{ isComplete: true, deletedAt: null }] }

  if (communityIds?.length > 0) {
    query['community._id'] = { $in: communityIds }
  }
  if (buildingIds?.length > 0 && version !== '1') {
    query['building._id'] = { $in: buildingIds }
  }

  if (floorplanNames?.length > 0) {
    const floorplanBasicQuery = floorplanNames
      .filter(Boolean)
      .map((layout) => new RegExp(`\\b${escapeRegex(layout)}(?!\\w)`, 'i'))

    const floorplanBuildingQuery = floorplanNames.filter(Boolean).reduce(
      (acc, layout) => {
        const [building, floorplan] = layout.split(' ||| ')
        if (!building || !floorplan) return acc
        acc.building.push(new RegExp(`\\b${escapeRegex(building)}(?!\\w)`, 'i'))
        acc.floorplan.push(
          new RegExp(`\\b${escapeRegex(floorplan)}(?!\\w)`, 'i')
        )
        return acc
      },
      {
        floorplan: [],
        building: []
      }
    )

    query.$and.push({
      $or: [
        { 'floorPlan.name': { $in: floorplanBasicQuery } },
        {
          $and: [
            {
              'building.name': {
                $in: floorplanBuildingQuery?.building?.filter(Boolean)
              }
            },
            {
              'floorPlan.name': {
                $in: floorplanBuildingQuery?.floorplan?.filter(Boolean)
              }
            }
          ]
        }
      ]
    })
  }
  if (units?.length > 0) {
    const unitBasicQuery = units
      .filter(Boolean)
      .map((unit) => new RegExp(`${escapeRegex(unit)}`, 'i'))

    const unitBuildingQuery = units.filter(Boolean).reduce(
      (acc, u) => {
        const [building, unit] = u.split(' ||| ')
        if (!building || !unit) return acc
        acc.building.push(new RegExp(`${escapeRegex(building)}`, 'i'))
        acc.unit.push(new RegExp(`${escapeRegex(unit)}`, 'i'))
        return acc
      },
      {
        unit: [],
        building: []
      }
    )
    query.$and.push({
      $or: [
        { unit: { $in: unitBasicQuery } },
        {
          $and: [
            {
              'building.name': {
                $in: unitBuildingQuery.building?.filter(Boolean)
              }
            },
            { unit: { $in: unitBuildingQuery.unit?.filter(Boolean) } }
          ]
        }
      ]
    })
  }

  if (version === '1') {
    return SpaceModel.find(query).populate('community').lean()
  }

  return SpaceModel.find(query)
    .populate('community')
    .populate('building')
    .lean()
}

export const findAllSpacesByCommunityIdAndBedrooms = async (
  communityId?: string,
  showAllVisible = false,
  buildingId?: string,
  bedrooms?: string[],
  version?: string
) => {
  const query = {
    isComplete: true,
    nodes: { $exists: true, $ne: [] }
  }

  if (!showAllVisible) {
    query['isVisible'] = true
  }

  if (communityId) {
    query['community._id'] = communityId
  } else if (buildingId && version !== '1') {
    query['building._id'] = buildingId
  }

  if (bedrooms !== undefined && bedrooms.length > 0) {
    query['$or'] = []
    for (const bedroom of bedrooms) {
      query['$or'].push({ bedrooms: parseInt(bedroom) })
    }
  }

  return SpaceModel.find(query).sort({ bedrooms: 1 }).lean().exec()
}

export const findSpaceByToken = async (token: string) => {
  return SpaceModel.findOne({
    token
  }).lean()
}

export const findOneSpaceByQuery = async (
  query: any,
  projection?: string
): Promise<Space | null> => {
  return SpaceModel.findOne(query).select(projection).lean()
}

export const updateSpaceBuilding = async (
  id: string,
  address: any,
  alternativeName: string
) => {
  return SpaceModel.updateMany(
    { 'building._id': id },
    {
      $set: {
        'building.address': address,
        'building.alternativeName': alternativeName
      }
    }
  )
}

export const findSpacesByQuery = async (
  query: any,
  projection?: string,
  options?: QueryOptions,
  sort?: Record<string, 1 | -1>
): Promise<Partial<Space>[]> => {
  return SpaceModel.find(query)
    .select(projection)
    .sort(sort)
    .lean()
    .setOptions(options)
}

export const deleteSpaceById = async (id: string): Promise<void> => {
  await SpaceModel.updateOne({ _id: id }, { deletedAt: new Date() })
}

export async function findOneByQuery(
  query: { [key: string]: any },
  projection?: string | Record<string, object | string | number>
): Promise<Space | undefined> {
  return SpaceModel.findOne(query).select(projection).lean()
}

export async function findManyByQuery(
  query: { [key: string]: any },
  projection?: string
): Promise<Space[]> {
  return SpaceModel.find(query).select(projection).lean()
}

export async function countSpacesByQuery(query: {
  [key: string]: any
}): Promise<number> {
  return SpaceModel.countDocuments(query)
}

export async function updateOne(
  id: string,
  space: Partial<Space>
): Promise<void> {
  await SpaceModel.updateOne({ _id: id }, { $set: space })
}

export async function updateMany(
  query: { [key: string]: any },
  space: Partial<Space>
): Promise<void> {
  await SpaceModel.updateMany(query, { $set: space })
}

export async function findAllSpaces(query: SpaceQuery, projection?: string) {
  return SpaceModel.find(query).select(projection).lean()
}

export async function bulkUpdateById(spaces: Partial<Space>[]) {
  const updateData = spaces.map((space) => {
    const { _id, ...rest } = space
    return {
      updateOne: {
        filter: { _id },
        update: { $set: rest }
      }
    }
  })
  const resp = await SpaceModel.bulkWrite(updateData)
  return resp
}

export const duplicateKeyError = MONGO_ERROR_CODES.DUPLICATED_KEYS
