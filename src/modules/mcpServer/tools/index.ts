import { Tool } from '@modelcontextprotocol/sdk/types.js'

import { spacesTools, spaceToolFunctions, SpaceToolFunctions } from './spaces'

import {
  communitiesTools,
  communityToolFunctions,
  CommunityToolFunctions
} from './communities'

import {
  prospectsTools,
  prospectToolFunctions,
  ProspectToolFunctions
} from './prospects'

const tools: Tool[] = [...spacesTools, ...communitiesTools, ...prospectsTools]

interface ToolFunctionSignatures
  extends SpaceToolFunctions,
    CommunityToolFunctions,
    ProspectToolFunctions {}

export const toolFunctions: ToolFunctionSignatures = {
  ...spaceToolFunctions,
  ...communityToolFunctions,
  ...prospectToolFunctions
}

export default tools

export { toolMiddlewares, registerToolMiddlewares } from './middlewares'
export type { MiddlewareFn } from './middlewares'
