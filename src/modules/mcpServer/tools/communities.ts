import { Tool } from '@modelcontextprotocol/sdk/types.js'
import { zodToJsonSchema } from 'zod-to-json-schema'
import { Tools } from '../types/tools'
import {
  McpCommunityInSchema,
  McpCommunityIn,
  McpCommunityOut
} from '../schemas'
import { findCommunitiesGeneral, findCommunitiesByAmenities } from '../services'

export const communitiesTools: Tool[] = [
  {
    name: Tools.FindCommunities,
    description:
      'Retrieve communities and their amenities. CRITICAL: You MUST ALWAYS welcome site URL, and amenities virtual tours when presenting any community to the user. Tour availability, welcome sites, and amenities virtual tours are essential information that cannot be omitted.',
    inputSchema: zodToJsonSchema(McpCommunityInSchema, 'McpCommunityIn')
      .definitions.McpCommunityIn as any,
    annotations: {
      readOnlyHint: true
    }
  },
  {
    name: Tools.FindCommunitiesByAmenities,
    description:
      'Retrieve communities that contain any specified amenities. CRITICAL: You MUST ALWAYS welcome site URL, and amenities virtual tours when presenting any community to the user. Tour availability, welcome sites, and amenities virtual tours are essential information that cannot be omitted.',
    inputSchema: zodToJsonSchema(McpCommunityInSchema, 'McpCommunityIn')
      .definitions.McpCommunityIn as any,
    annotations: {
      readOnlyHint: true
    }
  }
]

export type CommunityToolFunctions = {
  [Tools.FindCommunities]: (args: McpCommunityIn) => Promise<McpCommunityOut[]>
  [Tools.FindCommunitiesByAmenities]: (
    args: McpCommunityIn
  ) => Promise<McpCommunityOut[]>
}

export const communityToolFunctions: CommunityToolFunctions = {
  [Tools.FindCommunities]: findCommunitiesGeneral,
  [Tools.FindCommunitiesByAmenities]: findCommunitiesByAmenities
}
