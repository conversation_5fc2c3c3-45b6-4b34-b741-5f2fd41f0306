import { McpError, ErrorCode } from '@modelcontextprotocol/sdk/types.js'
import { logError } from '@core/log'

/**
 * Translate Peek domain or HTTP-style exceptions into MCP-compliant errors.
 * Falls back to INTERNAL_ERROR while logging unexpected errors.
 */
export function toMcpError(err: any): McpError {
  if (err instanceof McpError) return err

  const status: number | undefined = err?.status ?? err?.statusCode

  switch (status) {
    case 400:
      return new McpError(ErrorCode.InvalidParams, err.message)
    case 401:
    case 403:
    case 409:
      return new McpError(ErrorCode.InvalidRequest, err.message)
    case 404:
      return new McpError(ErrorCode.MethodNotFound, err.message)
    default:
      logError('mcp-error-mapper', 'Unexpected error', { err })
      return new McpError(ErrorCode.InternalError, 'Internal server error')
  }
}
