import { McpCommunityIn, McpCommunityOut } from '../schemas'
import {
  amenityToMcpAmenityOut,
  amenityMcpOutProjection,
  communityToMcpCommunityOut,
  communityMcpOutProjection
} from '../utils/communityUtils'
import { buildCommunityQuery } from '../utils/queryBuilder'
import { SpaceType } from '@modules/communities/types/spaceType'
import { ProjectionOptions } from '@core/repositories/CrudRepository'
import { communityRepositoryMongoDb } from '@modules/communities/repositories/v2/communityRepository'
import { spaceRepositoryMongoDb } from '@modules/spacex/repositories/spaceRepository'
import { findCommunitiesByAmenities as findCommunitiesByAmenitiesAggregate } from '@modules/communities/repositories/v2/spaceAmenitiesAggregate'
import { logDebug } from '@core/log'

const location = 'mcp-communities-service'

// Utility method to add amenities information to the raw community list.
const enrichCommunitiesWithAmenities = async (
  communities: any[]
): Promise<McpCommunityOut[]> => {
  const communitiesOut: McpCommunityOut[] = []

  for (const community of communities) {
    const amenities = await spaceRepositoryMongoDb.findAll(
      {
        'community._id': community._id,
        type: SpaceType.Amenity
      },
      {
        projection: amenityMcpOutProjection as ProjectionOptions,
        skipAclScope: true
      }
    )

    const amenitiesOut = amenities.map((amenity) =>
      amenityToMcpAmenityOut(amenity)
    )

    communitiesOut.push(communityToMcpCommunityOut(community, amenitiesOut))
  }

  return communitiesOut
}

export const findCommunitiesGeneral = async (
  query: McpCommunityIn
): Promise<McpCommunityOut[]> => {
  const { skip = 0, limit = 10 } = query
  logDebug(location, 'findCommunitiesGeneral called', { query })

  const communities = await communityRepositoryMongoDb.findAll(
    buildCommunityQuery(query),
    {
      skip,
      limit,
      projection: communityMcpOutProjection as ProjectionOptions
    }
  )

  const result = await enrichCommunitiesWithAmenities(communities)
  logDebug(location, 'findCommunitiesGeneral result', { count: result.length })
  return result
}

export const findCommunitiesByAmenities = async (
  query: McpCommunityIn
): Promise<McpCommunityOut[]> => {
  const { skip = 0, limit = 10 } = query
  logDebug(location, 'findCommunitiesByAmenities called', { query })

  if (!query.amenities || query.amenities.length === 0) {
    logDebug(location, 'No amenities provided, returning empty list')
    return []
  }

  const match = buildCommunityQuery(query)

  const communities = await findCommunitiesByAmenitiesAggregate(
    query.amenities,
    match,
    {
      skip,
      limit,
      projection: communityMcpOutProjection as ProjectionOptions
    }
  )

  const result = await enrichCommunitiesWithAmenities(communities)
  logDebug(location, 'findCommunitiesByAmenities result', {
    count: result.length
  })

  return result
}
