import crypto from 'crypto'
import { customAlphabet } from 'nanoid'
import { ObjectId } from 'mongodb'
import { Rotation } from '@modules/communities/types/rotation'
import startCase from 'lodash/startCase'
import { Position } from '@modules/communities/types/node'
import { Address } from '@modules/communities/types/address'
import { ADMIN_ORG_DOMAINS } from '@modules/users/types/user'

export type ProjectionFields = { [field: string]: 0 | 1 | string }
export const fieldsToProjection = (
  fields: string | ProjectionFields,
  separator: string
) => {
  if (typeof fields !== 'string') return fields
  return fields.split(separator).reduce((acc, field) => {
    if (field) acc[field] = 1
    return acc
  }, {})
}

export const generateHash = (secret: string, salt: string): string => {
  const hash = crypto.scryptSync(secret, salt, 64).toString('hex')

  return hash
}

export const customNanoId = (size: number) => {
  const alphabet =
    '0123456789abcdef<PERSON>i<PERSON>lmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ$@'
  return customAlphabet(alphabet, size)()
}

export const escapeRegex = (str: string) => {
  return str.replace(/[-[\]{}()*+?.,\\^$|#\s]/g, '\\$&')
}

export const pick = <T extends object>(obj: T, keys: Array<keyof T>) => {
  return keys.reduce((acc, key) => {
    if (key in obj) {
      acc[key] = obj[key]
    }
    return acc
  }, {} as T)
}

export const removeUndef = <T>(obj: T): Partial<T> => {
  return JSON.parse(JSON.stringify(obj)) as Partial<T>
}

export const generateTempPassword = (length: number): string => {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
  let tempPassword = ''
  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * chars.length)
    tempPassword += chars[randomIndex]
  }
  return tempPassword
}

export const flattenObjectKeys = (obj, prefix = '') => {
  return Object.keys(obj).reduce((acc, key) => {
    const newKey = prefix ? `${prefix}.${key}` : key
    if (
      !Array.isArray(obj[key]) &&
      typeof obj[key] === 'object' &&
      !(obj[key] instanceof Date) &&
      obj[key] !== null &&
      !ObjectId.isValid(obj[key])
    ) {
      const nestedKeys = flattenObjectKeys(obj[key], newKey)
      Object.assign(acc, nestedKeys)
    } else {
      acc[newKey] = obj[key]
    }
    return acc
  }, {})
}

export const extractDigits = (str: string): string => {
  return str.replace(/\D/g, '')
}

export const convertCoordinateToPitchAndYaw = (
  position: Position
): Pick<Rotation, 'pitch' | 'yaw'> => {
  const { x, y, z } = position

  const yaw = -Math.atan2(x, z)
  const pitch = Math.atan2(y, Math.sqrt(Math.pow(x, 2) + Math.pow(z, 2)))
  const yawInDegree = (yaw * 180) / 3.14159
  const pitchInDegree = (pitch * 180) / 3.14159
  return { pitch: pitchInDegree, yaw: yawInDegree }
}

export const convertDaysToMS = (days: number): number => {
  return days * 24 * 60 * 60 * 1000
}

export const convertMinutesToMS = (minutes: number): number => {
  return minutes * 60 * 1000
}

export const convertSecondsToMS = (seconds: number): number => {
  return seconds * 1000
}

export const getEnvVariable = (key: string): string => {
  const value = process.env[key]
  if (!value) {
    throw new Error(`Missing environment variable ${key}`)
  }
  return value
}

export const convertStringToBoolean = (value: string): boolean => {
  return value === 'true'
}

export function sleep(ms: number) {
  return new Promise((resolve) => setTimeout(resolve, ms))
}

export function getRandomTimeMs(limit = 401): number {
  return Math.floor(Math.random() * limit) + 100
}

export function decodeBase64(value: string): string {
  try {
    return atob(value)
  } catch (error) {
    throw new Error('Failed to decode base64')
  }
}

export class ReplaceableString {
  private input: string

  constructor(input: string) {
    this.input = input
  }

  replaceWord(searchWord: string, replaceWord: string): ReplaceableString {
    this.input = this.input.replace(new RegExp(searchWord, 'g'), replaceWord)
    return this
  }

  toString(): string {
    return this.input
  }
}

export function toTitleCase(value: string): string {
  return startCase(value).replace(/\s/g, '')
}

export function convertMSToHoursAndMinutes(ms: number): string {
  const hours = Math.floor(ms / 3600000)
  const minutes = Math.floor((ms % 3600000) / 60000)
  const hoursString = hours > 0 ? `${hours}h` : ''
  const minutesString = minutes > 0 ? `${minutes}m` : ''
  return `${hoursString} ${minutesString}`.trim()
}

export const generateDate = (): Date => {
  return new Date()
}

export function isAddressEqual(address1: Address, address2: Address) {
  return (
    address1.street === address2.street &&
    address1.city === address2.city &&
    address1.state === address2.state &&
    address1.postalCode === address2.postalCode
  )
}

export function isNumeric(str: string) {
  return !isNaN(Number(str)) && !isNaN(parseFloat(str))
}

export function assignObject(obj: any) {
  return Object.assign({}, obj)
}

export function buildExclusionProjection(paths: string[]): Record<string, 0> {
  const projection: Record<string, 0> = {}

  for (const path of paths) {
    projection[path] = 0
  }

  return projection
}

export function isOrgAdminEmail(email: string): boolean {
  const [, domain] = email.split('@')

  return ADMIN_ORG_DOMAINS.includes(domain)
}

export function degToRad(deg: number) {
  return deg * (Math.PI / 180)
}

export function hotspotToPosition(
  pitchDeg: number,
  yawDeg: number,
  radius = 480
) {
  const pitch = degToRad(pitchDeg)
  const yaw = degToRad(yawDeg)

  const correctedYaw = yaw + Math.PI / 2

  const x = radius * Math.cos(pitch) * Math.sin(correctedYaw)
  const y = radius * Math.sin(pitch)
  const z = radius * Math.cos(pitch) * Math.cos(correctedYaw)

  return { x: -x, y, z }
}
