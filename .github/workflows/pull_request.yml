name: PR Pipeline

on:
  pull_request:
    types: [opened, reopened, synchronize]

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  install_dependencies:
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest
    name: Install dependencies

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Yarn install
        uses: peek-tech/tools-infra-actions/.github/actions/install@main
        with:
          cache_key: "node_modules"
          npm_token: ${{ secrets.NPM_TOKEN }}

  tsc:
    name: Typescript Check
    needs: [install_dependencies]
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: TSC check
        uses: peek-tech/tools-infra-actions/.github/actions/command@main
        with:
          dependencies_cache_key: "node_modules"
          command: ts:check

  lint:
    name: <PERSON><PERSON>
    needs: [install_dependencies]
    runs-on: ubuntu-latest
    container:
      image: ghcr.io/peek-tech/node20:latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: <PERSON><PERSON>
        uses: peek-tech/tools-infra-actions/.github/actions/command@main
        with:
          dependencies_cache_key: "node_modules"
          command: lint

  test:
    name: Unit tests
    needs: [tsc, lint]
    runs-on: 8_32_runner
    container:
      image: ghcr.io/peek-tech/node20:latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: TSC check
        uses: peek-tech/tools-infra-actions/.github/actions/command@main
        with:
          dependencies_cache_key: "node_modules"
          command: test:unit:report

      - name: Jest Coverage Comment
        uses: MishaKav/jest-coverage-comment@main
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          coverage-summary-path: ./coverage/coverage-summary.json
          badge-title: Coverage
          title: Code coverage
          summary-title: Summary
          hide-comment: false
          create-new-comment: false
          hide-summary: false

  e2e-test:
    name: E2E tests
    needs: [tsc, lint]
    runs-on: 8_32_runner
    container:
      image: ghcr.io/peek-tech/node20:latest
    env:
      FORCE_COLOR: 1

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Restore base test results
        uses: actions/cache@v4
        with:
          path: jest-results.json
          key: jest-results-${{ github.event.pull_request.base.sha }}
          restore-keys: |
            jest-results-

      - name: CP base json
        run: cp jest-results.json jest-base.json

      - name: Test
        uses: peek-tech/tools-infra-actions/.github/actions/command@main
        continue-on-error: true
        with:
          dependencies_cache_key: "node_modules"
          command: test:e2e:json

      - name: Compare Jest test results
        run: |
            echo "🔍 Base (main) failing tests:"
            jq -r '
              .testResults[]
              | .assertionResults[]
              | select(.status == "failed")
              | "\(.ancestorTitles | join(" > ")) > \(.title)"
            ' jest-base.json | sort > base-failures.txt
            cat base-failures.txt
        
            echo "🔍 PR failing tests:"
            jq -r '
              .testResults[]
              | .assertionResults[]
              | select(.status == "failed")
              | "\(.ancestorTitles | join(" > ")) > \(.title)"
            ' jest-results.json | sort > pr-failures.txt
            cat pr-failures.txt
        
            echo "🔍 Comparing for regressions..."
            new_regressions=$(comm -13 base-failures.txt pr-failures.txt)
            fixed_tests=$(comm -23 base-failures.txt pr-failures.txt)
        
            if [ -n "$new_regressions" ]; then
              {
                echo '```'
                echo "❌ New test regressions detected:"
                echo
                echo "$new_regressions"
                echo '```'
              } > diff_output.txt
        
              echo "REGRESSIONS_FOUND=true" >> $GITHUB_ENV
        
            else
              echo "✅ No new test failures introduced."
        
              if [ -n "$fixed_tests" ]; then
                cat <<EOF > diff_output.txt
            🎉 **Test Fix Detected!** 🎉
            
            Great job! This PR fixes the following previously failing test(s):
            
            \`\`\`
            $fixed_tests
            \`\`\`
            
            Here is a picture of kevin, because you deserve it:
            
            ![kevin](https://media1.tenor.com/m/40BdfMplFhwAAAAC/cat-funny.gif)
            EOF
        
                echo "FIXED_TESTS_FOUND=true" >> $GITHUB_ENV
              fi
            fi

      - name: Post regressions to PR
        if: ${{ env.REGRESSIONS_FOUND == 'true' }}
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: jest-e2e-diff
          hide_and_recreate: true
          hide_classify: "OUTDATED"
          path: diff_output.txt
      
      - name: Post congratulations to PR
        if: ${{ env.FIXED_TESTS_FOUND == 'true' }}
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          header: jest-e2e-diff
          hide_and_recreate: true
          hide_classify: "OUTDATED"
          path: diff_output.txt

      - name: Fail job
        if: ${{ env.REGRESSIONS_FOUND == 'true' }}
        run: |
          echo "❌ New test regressions detected"
          exit 1
