service: peek-backend-prospects-serverless

useDotenv: true

custom:
  sentry:
    enabled:
      prod: true
      dev: false
  esbuild:
    bundle: true
    packager: yarn
    platform: node
    target: node18
    concurrency: 4
    format: cjs
    banner:
      js: |
        const Sentry = require("@sentry/aws-serverless");
        
        const consoleLevels = [];
        if (process.env.ENABLE_SENTRY_CONSOLE_ERROR === "true") {
          consoleLevels.push("error");
        }
        if (process.env.ENABLE_SENTRY_CONSOLE_WARN === "true") {
          consoleLevels.push("warn");
        }

        Sentry.init({
          dsn: process.env.SENTRY_DSN,
          enabled: process.env.SENTRY_ENABLED?.toString() === 'true',
          integrations: [Sentry.captureConsoleIntegration({ levels: consoleLevels })],
        });
        Sentry.setTag("stack_name", "${self:service}-${self:provider.stage}");
    footer:
      js: |
        module.exports = { 
          handler: Sentry.wrapHandler(handler)
        };
  dotenv:
    include:
      - MONGODB_URL
      - AGENT_DASHBOARD_WEB_URL
    logging: true

provider:
  name: aws
  runtime: nodejs18.x
  stage: ${opt:stage, "dev"}
  region: "us-east-1"
  memorySize: 512
  timeout: 10
  logRetentionInDays: 90
  versionFunctions: false
  layers:
    - arn:aws:lambda:us-east-1:943013980633:layer:SentryNodeServerlessSDKv9:11
  environment:
    SENTRY_DSN: ${ssm:/${self:provider.stage}/SENTRY_SERVERLESS_DSN}
    SENTRY_ENABLED: ${self:custom.sentry.enabled.${opt:stage, 'dev'}}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - SQS:SendMessage
        - SQS:DeleteMessage
        - SQS:ReceiveMessage
        - SQS:GetQueueUrl
        - SQS:ListQueues
      Resource: "*"
    - Effect: "Allow"
      Action:
        - sns:Publish
        - sns:GetTopicAttributes
        - sns:Receive
      Resource: "*"
    - Effect: "Allow"
      Action:
        - "s3:PutObject"
        - "s3:GetObject"
        - "s3:ListBucket"
        - "s3:DeleteObject"
      Resource: "*"
    - Effect: 'Allow'
      Action:
        - secretsmanager:ListSecrets
        - secretsmanager:GetSecretValue
        - secretsmanager:PutSecretValue
        - secretsmanager:UpdateSecret
      Resource: '*'

functions:
  SyncProspectStartFunction:
    handler: src/modules/dataExchange/serverless/syncProspects.handler
    name: ${self:provider.stage}-sync-prospects-start-function
    reservedConcurrency: 1
    timeout: 900
    environment:
      PROSPECT_SYNC_QUEUE:
        Ref: SyncProspectsWorkerQueue
    events:
      - schedule:
          rate: cron(0 4 * * ? *) # 4am UTC = 12am EST

  SyncProspectEventsStartFunction:
    handler: src/modules/dataExchange/serverless/syncProspectEvents.handler
    name: ${self:provider.stage}-sync-prospect-events-start-function
    reservedConcurrency: 1
    timeout: 900
    environment:
      SYNC_PROSPECT_EVENTS_TOPIC: arn:aws:sns:us-east-1:112470036249:${self:provider.stage}-sync-prospect-events-topic
    #    events:
    #      # at 10pm New York time in DST
    #      - schedule:
    #          rate: cron(0 2 11-31 3 ? *) # DST portion of March -4
    #      - schedule:
    #          rate: cron(0 2 * 4-10 ? *) # full DST months -4
    #      - schedule:
    #          rate: cron(0 2 1-3 11 ? *) # DST portion of November -4
    #      # at 10pm New York time in non-DST
    #      - schedule:
    #          rate: cron(0 3 * 1-2,12 ? *) # full non-DST months -5
    #      - schedule:
    #          rate: cron(0 3 1-10 3 ? *) # non-DST portion of March -5
    #      - schedule:
    #          rate: cron(0 3 4-31 11 ? *) # non-DST portion of November -5

  SyncProspectsWorkerFunction:
    handler: src/modules/dataExchange/serverless/syncProspectsWorker.handler
    name: ${self:provider.stage}-sync-prospects-worker-function
    reservedConcurrency: 1
    timeout: 100
    environment:
      PROSPECT_SYNC_TOPIC: arn:aws:sns:us-east-1:112470036249:${self:provider.stage}-sync-prospect-topic
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - SyncProspectsWorkerQueue
              - Arn

  SyncEntrataProspectStatusFunction:
    handler: src/modules/entrata/serverless/syncEntrataProspectStatus.handler
    name: ${self:provider.stage}-sync-entrata-prospect-status-function
    reservedConcurrency: 1
    timeout: 100
    environment:
      ENTRATA_API_KEY: ${ssm:/${self:provider.stage}/ENTRATA_API_KEY}
      ENTRATA_API_URL: ${ssm:/${self:provider.stage}/ENTRATA_API_URL}
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - SyncEntrataProspectStatusQueue
              - Arn

  SyncEntrataProspectEventsFunction:
    handler: src/modules/entrata/serverless/syncEntrataProspectEvents.handler
    name: ${self:provider.stage}-sync-entrata-prospect-events-function
    reservedConcurrency: 3
    timeout: 100
    environment:
      SYNC_PROSPECT_EVENTS_QUEUE_URL:
        Ref: SyncEntrataProspectEventsWorkerQueue
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - SyncEntrataProspectEventsQueue
              - Arn

  SyncEntrataProspectEventsWorkerFunction:
    handler: src/modules/entrata/serverless/syncEntrataProspectEventsWorker.handler
    name: ${self:provider.stage}-sync-entrata-prospect-events-worker-function
    reservedConcurrency: 3
    timeout: 100
    environment:
      PROSPECTS_EVENTS_BUCKET:
        Ref: ProspectEventsBucket
      ENTRATA_API_KEY: ${ssm:/${self:provider.stage}/ENTRATA_API_KEY}
      ENTRATA_API_URL: ${ssm:/${self:provider.stage}/ENTRATA_API_URL}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - SyncEntrataProspectEventsWorkerQueue
              - Arn

  SyncYardiProspectEventsFunction:
    handler: src/modules/yardi/serverless/syncYardiProspectEvents.handler
    name: ${self:provider.stage}-sync-yardi-prospect-events-function
    reservedConcurrency: 3
    timeout: 100
    environment:
      LOG_LEVEL: ${ssm:/${self:provider.stage}/LOG_LEVEL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      PEEK_APP_YARDI_LICENSE: ${ssm:/${self:provider.stage}/PEEK_APP_YARDI_LICENSE}
      PROSPECTS_EVENTS_BUCKET:
        Ref: ProspectEventsBucket
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - SyncYardiProspectEventsQueue
              - Arn

  SyncYardiProspectFunction:
    handler: src/modules/yardi/serverless/syncYardiProspectStatus.handler
    name: ${self:provider.stage}-sync-yardi-prospect-status-function
    reservedConcurrency: 3
    timeout: 300
    environment:
      LOG_LEVEL: ${ssm:/${self:provider.stage}/LOG_LEVEL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      PEEK_APP_YARDI_LICENSE: ${ssm:/${self:provider.stage}/PEEK_APP_YARDI_LICENSE}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - SyncYardiProspectStatusQueue
              - Arn

  ReviewYardiProspectFunction:
    handler: src/modules/yardi/serverless/reviewYardiProspect.handler
    name: ${self:provider.stage}-review-yardi-prospect-function
    reservedConcurrency: 1
    events:
      - schedule:
          rate: rate(15 minutes)
    environment:
      PEEK_APP_YARDI_LICENSE: ${ssm:/${self:provider.stage}/PEEK_APP_YARDI_LICENSE}
      YARDI_UPDATE_COMMENTS_QUEUE:
        Ref: UpdateYardiProspectWithoutAppointmentQueue
    timeout: 900

  UpdateYardiProspectWithoutAppointmentFunction:
    handler: src/modules/yardi/serverless/updateYardiProspectWithoutAppointment.handler
    name: ${self:provider.stage}-update-yardi-prospect-without-appointment-function
    reservedConcurrency: 1
    timeout: 900
    environment:
      PEEK_APP_YARDI_LICENSE: ${ssm:/${self:provider.stage}/PEEK_APP_YARDI_LICENSE}
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - UpdateYardiProspectWithoutAppointmentQueue
              - Arn

  # PiSummaryCrmTours
  StartSummaryCrmTourFunction:
    handler: src/modules/analytics/serverless/startPiSummary.handler
    name: ${self:provider.stage}-start-pi-summary-tours-function
    reservedConcurrency: 1
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SYNC_PI_SUMMARY_QUEUE: { Ref: SummaryCrmToursQueue }
    timeout: 100
    events:
      - schedule:
          rate: cron(0 0 * * ? *)

  SendSummaryCalvinFunction:
    handler: src/modules/analytics/serverless/sendPiSummaryCalvin.handler
    name: ${self:provider.stage}-send-summary-calvin-function
    reservedConcurrency: 1
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      CALVIN_SUMMARY_TOUR_QUEUE_URL: ${ssm:/${self:provider.stage}/CALVIN_SUMMARY_TOUR_QUEUE_URL}
    timeout: 600
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - SummaryCrmToursQueue
              - Arn
          functionResponseType: ReportBatchItemFailures

  SendYardiSummaryFunction:
    handler: src/modules/yardi/serverless/sendYardiPiSummary.handler
    name: ${self:provider.stage}-send-yardi-summary
    reservedConcurrency: 3
    environment:
      LOG_LEVEL: ${ssm:/${self:provider.stage}/LOG_LEVEL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      PEEK_APP_YARDI_LICENSE: ${ssm:/${self:provider.stage}/PEEK_APP_YARDI_LICENSE}
    timeout: 600
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - SendYardiSummaryQueue
              - Arn

  SendEntrataSummaryFunction:
    handler: src/modules/entrata/serverless/sendEntrataPiSummary.handler
    name: ${self:provider.stage}-send-entrata-summary
    reservedConcurrency: 3
    environment:
      LOG_LEVEL: ${ssm:/${self:provider.stage}/LOG_LEVEL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      ENTRATA_TIMEZONE: ${ssm:/${self:provider.stage}/ENTRATA_TIMEZONE}
    timeout: 600
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - SendEntrataSummaryQueue
              - Arn

  SendFunnelSummaryFunction:
    handler: src/modules/funnel/serverless/sendFunnelPiSummary.handler
    name: ${self:provider.stage}-send-funnel-summary
    reservedConcurrency: 3
    environment:
      LOG_LEVEL: ${ssm:/${self:provider.stage}/LOG_LEVEL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      FUNNEL_TIMEZONE: ${ssm:/${self:provider.stage}/FUNNEL_TIMEZONE}
    timeout: 600
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - SendFunnelSummaryQueue
              - Arn

resources:
  Resources:
    ProspectEventsBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:provider.stage}-prospect-events-bucket
        AccessControl: Private

    SyncProspectTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: ${self:provider.stage}-sync-prospect-topic

    SyncProspectEventsTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: ${self:provider.stage}-sync-prospect-events-topic

    SendSummaryTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: ${self:provider.stage}-pi-summary-topic

    SyncProspectSnsToQueueSQSPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Sid: "allow-sns-messages"
              Effect: "Allow"
              Principal:
                Service:
                  - "sns.amazonaws.com"
              Resource:
                Fn::GetAtt:
                  - SyncEntrataProspectStatusQueue
                  - Arn
              Action: "SQS:SendMessage"
              Condition:
                ArnEquals:
                  "aws:SourceArn":
                    Ref: SyncProspectTopic
            - Sid: "allow-sns-messages"
              Effect: "Allow"
              Principal:
                Service:
                  - "sns.amazonaws.com"
              Resource:
                Fn::GetAtt:
                  - SyncYardiProspectStatusQueue
                  - Arn
              Action: "SQS:SendMessage"
              Condition:
                ArnEquals:
                  "aws:SourceArn":
                    Ref: SyncProspectTopic
        Queues:
          - Ref: SyncEntrataProspectStatusQueue
          - Ref: SyncYardiProspectStatusQueue

    SyncProspectEventsSnsToQueueSQSPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: "2012-10-17"
          Statement:
            - Sid: "allow-sns-messages"
              Effect: "Allow"
              Principal:
                Service:
                  - "sns.amazonaws.com"
              Resource:
                Fn::GetAtt:
                  - SyncEntrataProspectEventsQueue
                  - Arn
              Action: "SQS:SendMessage"
              Condition:
                ArnEquals:
                  "aws:SourceArn":
                    Ref: SyncProspectEventsTopic
            - Sid: "allow-sns-messages"
              Effect: "Allow"
              Principal:
                Service:
                  - "sns.amazonaws.com"
              Resource:
                Fn::GetAtt:
                  - SyncYardiProspectEventsQueue
                  - Arn
              Action: "SQS:SendMessage"
              Condition:
                ArnEquals:
                  "aws:SourceArn":
                    Ref: SyncProspectEventsTopic
        Queues:
          - Ref: SyncEntrataProspectEventsQueue
          - Ref: SyncYardiProspectEventsQueue
    SyncProspectsWorkerQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-prospects-worker-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SyncProspectsWorkerQueueDLQ
              - Arn
          maxReceiveCount: 3
    SyncProspectsWorkerQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-prospects-worker-queue-dlq
    SyncProspectWorkerQueueSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - SyncProspectsWorkerQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SyncProspectTopic

    SummarySnsToQueueSQSPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - SendYardiSummaryQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SendSummaryTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - SendEntrataSummaryQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SendSummaryTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - SendFunnelSummaryQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SendSummaryTopic
        Queues:
          - Ref: SendYardiSummaryQueue
          - Ref: SendEntrataSummaryQueue
          - Ref: SendFunnelSummaryQueue

    SyncEntrataProspectStatusQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-entrata-prospect-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SyncEntrataProspectStatusQueueDLQ
              - Arn
          maxReceiveCount: 3
    SyncEntrataProspectStatusQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-entrata-prospect-queue-dlq
    SyncEntrataProspectSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - SyncEntrataProspectStatusQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SyncProspectTopic
        FilterPolicy:
          service:
            - entrata

    SyncEntrataProspectEventsQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-entrata-prospect-events-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SyncEntrataProspectEventsQueueDLQ
              - Arn
          maxReceiveCount: 3
    SyncEntrataProspectEventsQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-entrata-prospect-events-queue-dlq
    SyncEntrataProspectEventSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - SyncEntrataProspectEventsQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SyncProspectEventsTopic
        FilterPolicy:
          service:
            - entrata

    SyncEntrataProspectEventsWorkerQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-entrata-prospect-events-worker-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SyncEntrataProspectEventsWorkerQueueDLQ
              - Arn
          maxReceiveCount: 3
    SyncEntrataProspectEventsWorkerQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-entrata-prospect-events-worker-queue-dlq

    SyncYardiProspectEventsQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-yardi-prospect-events-queue
        VisibilityTimeout: 100
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SyncYardiProspectEventsQueueDLQ
              - Arn
          maxReceiveCount: 3
    SyncYardiProspectEventsQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-yardi-prospect-events-queue-dlq
    SynYardiProspectEventsSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - SyncYardiProspectEventsQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SyncProspectEventsTopic
        FilterPolicy:
          service:
            - yardi

    SyncYardiProspectStatusQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-yardi-prospect-queue
        VisibilityTimeout: 300
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SyncYardiProspectStatusQueueDLQ
              - Arn
          maxReceiveCount: 3
    SyncYardiProspectStatusQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-yardi-prospect-queue-dlq
    SyncYardiProspectSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - SyncYardiProspectStatusQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SyncProspectTopic
        FilterPolicy:
          service:
            - yardi

    UpdateYardiProspectWithoutAppointmentQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-update-yardi-prospect-without-appointment-queue.fifo
        FifoQueue: true
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - UpdateYardiProspectWithoutAppointmentQueueDLQ
              - Arn
          maxReceiveCount: 3
    UpdateYardiProspectWithoutAppointmentQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        FifoQueue: true
        QueueName: ${self:provider.stage}-update-yardi-prospect-without-appointment-queue-dlq.fifo

    SummaryCrmToursQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-summary-crm-tours-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SummaryCrmToursQueueDLQ
              - Arn
          maxReceiveCount: 3
    SummaryCrmToursQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-summary-crm-tours-queue-dlq

    SendYardiSummaryQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-send-yardi-summary-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SendYardiSummaryQueueDLQ
              - Arn
          maxReceiveCount: 3
    SendYardiSummaryQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-send-yardi-summary-queue-dlq
    SendYardiSummaryQueueSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - SendYardiSummaryQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SendSummaryTopic
        FilterPolicy:
          service:
            - yardi

    SendEntrataSummaryQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-send-entrata-summary-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SendEntrataSummaryQueueDLQ
              - Arn
          maxReceiveCount: 3
    SendEntrataSummaryQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-send-entrata-summary-queue-dlq
    SendEntrataSummaryQueueSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - SendEntrataSummaryQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SendSummaryTopic
        FilterPolicy:
          service:
            - entrata

    SendFunnelSummaryQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-send-funnel-summary-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SendFunnelSummaryQueueDLQ
              - Arn
          maxReceiveCount: 3
    SendFunnelSummaryQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-send-funnel-summary-queue-dlq
    SendFunnelSummaryQueueSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - SendFunnelSummaryQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SendSummaryTopic
        FilterPolicy:
          service:
            - funnel

package:
  individually: true

plugins:
  - serverless-esbuild
  - ./serverless/sentry-log-error.js
