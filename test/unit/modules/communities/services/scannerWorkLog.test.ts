import * as authCore from '@core/auth'
import { Community } from '@modules/communities/types/community'
import { RoleAliases } from '@modules/users/types/role'
import { AuthUser } from '@modules/users/types/user'
import { mockCommunity } from '@test/helpers/community.helper'
import { mockUser } from '@test/helpers/user.helper'
import * as scannerWorkLogRepo from '@modules/communities/repositories/scannerWorkLog'
import * as communityService from '@modules/communities/services/community'
import * as scanRequestRepo from '@modules/communities/repositories/scanRequest'
import * as userService from '@modules/users/services/user'
import * as scanRequestService from '@modules/communities/services/scanRequest'
import * as userRepo from '@modules/users/repositories/user'
import {
  CreateScannerWorkLogDTO,
  FindScannerWorkLogsDTO
} from '@modules/communities/types/scannerWorkLog'
import { ObjectId } from '@modules/communities/types/id'
import {
  consolidateScannerWorkLogs,
  createScannerWorkLog,
  deleteScannerWorkLogById,
  findScannerWorkLogs,
  sendWorkLogRecapEmail,
  updateScannerWorkLogById
} from '@modules/communities/services/scannerWorkLog'
import { mockScanRequest } from '@test/helpers/scanRequest.helper'
import { ScanRequestStatus } from '@modules/communities/types/scanRequest'
import * as mail from '@core/mail'

describe('scan work logs', () => {
  let spyOnGetAuthUser: jest.SpyInstance
  let mockedUser: AuthUser
  let mockedCommunity: Community
  process.env.WORKLOG_JANITOR_ID = 'worklog-janitor-id'

  const resetMocks = () => {
    jest.resetAllMocks()

    mockedUser = { ...mockUser(), roleAlias: RoleAliases.ADMIN } as AuthUser
    mockedCommunity = mockCommunity()
  }
  describe('createScannerWorkLog', () => {
    let spyOnFindCommunityById: jest.SpyInstance
    let spyOnCreateScannerWorkLog: jest.SpyInstance

    beforeEach(() => {
      resetMocks()
      spyOnFindCommunityById = jest
        .spyOn(communityService, 'findCommunityById')
        .mockResolvedValue(mockedCommunity)

      spyOnCreateScannerWorkLog = jest
        .spyOn(scannerWorkLogRepo, 'createScannerWorkLog')
        .mockResolvedValue({} as any)

      spyOnGetAuthUser = jest
        .spyOn(authCore, 'getAuthUser')
        .mockReturnValue(mockedUser)

      jest
        .spyOn(
          scannerWorkLogRepo,
          'findScannerWorkLogByDateScannerAndCommunity'
        )
        .mockResolvedValue(null)
    })
    it('should create a scanner work log', async () => {
      const scannerWorkLogDTO: CreateScannerWorkLogDTO = {
        community: {
          _id: mockedCommunity._id.toString()
        },
        workingHours: 1,
        commuteTime: 1,
        workLogDate: new Date()
      }

      await createScannerWorkLog(scannerWorkLogDTO)
      expect(spyOnFindCommunityById).toHaveBeenCalledWith(
        scannerWorkLogDTO.community._id
      )
      expect(spyOnCreateScannerWorkLog).toHaveBeenCalledWith({
        scanner: {
          _id: mockedUser._id,
          name: mockedUser.name,
          email: mockedUser.email
        },
        community: mockedCommunity,
        scanRequests: [],
        spaces: [],
        workingHours: scannerWorkLogDTO.workingHours,
        commuteTime: scannerWorkLogDTO.commuteTime,
        workLogDate: new Date(
          scannerWorkLogDTO.workLogDate.setHours(0, 0, 0, 0)
        )
      })
    })
    it('should handle user not found', async () => {
      spyOnGetAuthUser.mockReturnValue(null)

      const scannerWorkLogDTO: CreateScannerWorkLogDTO = {
        community: {
          _id: mockedCommunity._id.toString()
        },
        workingHours: 1,
        commuteTime: 1,
        workLogDate: new Date()
      }

      try {
        await createScannerWorkLog(scannerWorkLogDTO)
      } catch (error) {
        expect(error).toBeDefined()
        expect(error.message).toBe('User not found.')
      }

      expect(spyOnFindCommunityById).not.toHaveBeenCalled()
      expect(spyOnCreateScannerWorkLog).not.toHaveBeenCalled()
    })
    it('should handle community not found', async () => {
      spyOnFindCommunityById.mockResolvedValue(null)

      const scannerWorkLogDTO: CreateScannerWorkLogDTO = {
        community: {
          _id: mockedCommunity._id.toString()
        },
        workingHours: 1,
        commuteTime: 1,
        workLogDate: new Date()
      }

      try {
        await createScannerWorkLog(scannerWorkLogDTO)
      } catch (error) {
        expect(error).toBeDefined()
        expect(error.message).toBe('Community not found.')
      }

      expect(spyOnFindCommunityById).toHaveBeenCalledWith(
        scannerWorkLogDTO.community._id
      )
      expect(spyOnCreateScannerWorkLog).not.toHaveBeenCalled()
    })
  })

  describe('findScannerWorkLogs', () => {
    let spyOnFindScannerWorkLogs: jest.SpyInstance
    beforeEach(() => {
      resetMocks()
      spyOnFindScannerWorkLogs = jest
        .spyOn(scannerWorkLogRepo, 'findScannerWorkLogs')
        .mockResolvedValue({} as any)
    })
    it('should find scanner work logs', async () => {
      const query: FindScannerWorkLogsDTO = {
        communityId: mockedCommunity._id.toString(),
        spaceId: new ObjectId().toString(),
        scannerId: new ObjectId().toString(),
        scanRequestId: new ObjectId().toString(),
        workingHours: 1,
        commuteTime: 1,
        offset: 0,
        limit: 10,
        orderBy: 'createdAt',
        order: 'asc',
        workLogDate: new Date()
      }
      await findScannerWorkLogs(query)
      expect(spyOnFindScannerWorkLogs).toHaveBeenCalledWith(query)
    })
  })
  describe('updateScannerWorkLogById', () => {
    let spyOnUpdateScannerWorkLog: jest.SpyInstance
    beforeEach(() => {
      resetMocks()
      spyOnUpdateScannerWorkLog = jest
        .spyOn(scannerWorkLogRepo, 'updateScannerWorkLog')
        .mockResolvedValue({} as any)
    })
    it('should update scanner work log by id', async () => {
      const id = new ObjectId().toString()
      await updateScannerWorkLogById(id, {
        workingHours: 1
      })
      expect(spyOnUpdateScannerWorkLog).toHaveBeenCalledWith(
        { _id: id },
        {
          workingHours: 1
        }
      )
    })
  })

  describe('deleteScannerWorkLogById', () => {
    let spyOnDeleteScannerWorkLog: jest.SpyInstance

    beforeEach(() => {
      resetMocks()
      spyOnDeleteScannerWorkLog = jest
        .spyOn(scannerWorkLogRepo, 'deleteScannerWorkLog')
        .mockResolvedValue({} as any)
    })

    it('should delete scanner work log by id', async () => {
      const id = new ObjectId().toString()
      await deleteScannerWorkLogById(id)
      expect(spyOnDeleteScannerWorkLog).toHaveBeenCalledWith({ _id: id })
    })
  })

  describe('consolidateScannerWorkLogs', () => {
    let spyOnFindScanRequestsByQuery: jest.SpyInstance
    let spyOnFindUserById: jest.SpyInstance
    let spyOnSkipScanRequest: jest.SpyInstance
    let spyOnFindScanRequestsByDateGroupedByScannerAndCommunity: jest.SpyInstance
    let spyOnFindScannerWorkLogByDateScannerAndCommunity: jest.SpyInstance
    let spyOnUpdateScannerWorkLog: jest.SpyInstance
    let spyOnCreateScannerWorkLog: jest.SpyInstance

    beforeEach(() => {
      resetMocks()
      spyOnFindScanRequestsByQuery = jest.spyOn(
        scanRequestRepo,
        'findScanRequestsByQuery'
      )
      spyOnFindUserById = jest.spyOn(userService, 'findUserById')
      spyOnSkipScanRequest = jest.spyOn(scanRequestService, 'skipScanRequest')
      spyOnFindScanRequestsByDateGroupedByScannerAndCommunity = jest.spyOn(
        scanRequestRepo,
        'findScanRequestsByDateGroupedByScannerAndCommunity'
      )
      spyOnFindScannerWorkLogByDateScannerAndCommunity = jest.spyOn(
        scannerWorkLogRepo,
        'findScannerWorkLogByDateScannerAndCommunity'
      )
      spyOnUpdateScannerWorkLog = jest.spyOn(
        scannerWorkLogRepo,
        'updateScannerWorkLog'
      )
      spyOnCreateScannerWorkLog = jest.spyOn(
        scannerWorkLogRepo,
        'createScannerWorkLog'
      )
    })

    it('should handle startDate with no scheduled scan requests', async () => {
      // Arrange
      const startDate = new Date('2023-06-01')

      spyOnFindScanRequestsByQuery.mockResolvedValue([])
      spyOnFindUserById.mockResolvedValue(mockedUser)
      spyOnFindScanRequestsByDateGroupedByScannerAndCommunity.mockResolvedValue(
        []
      )

      // Act
      await consolidateScannerWorkLogs(startDate)

      // Assert
      expect(spyOnFindScanRequestsByQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          status: ScanRequestStatus.SCHEDULED,
          scheduledFor: {
            $gte: startDate,
            $lte: expect.any(Date)
          }
        })
      )
      expect(spyOnFindUserById).toHaveBeenCalledWith('worklog-janitor-id')
      expect(spyOnSkipScanRequest).not.toHaveBeenCalled()
      expect(
        spyOnFindScanRequestsByDateGroupedByScannerAndCommunity
      ).toHaveBeenCalledWith(
        startDate,
        new Date(startDate.setHours(23, 59, 59, 999))
      )
      expect(
        spyOnFindScannerWorkLogByDateScannerAndCommunity
      ).not.toHaveBeenCalled()
      expect(spyOnCreateScannerWorkLog).not.toHaveBeenCalled()
    })
    it('should handle startDate with scheduled scan requests', async () => {
      // Arrange
      const startDate = new Date('2023-06-01')
      const mockedScanRequests = [
        mockScanRequest({ status: ScanRequestStatus.SCHEDULED })
      ]
      spyOnFindScanRequestsByQuery.mockResolvedValue(mockedScanRequests)
      spyOnFindUserById.mockResolvedValue(mockedUser)
      spyOnFindScanRequestsByDateGroupedByScannerAndCommunity.mockResolvedValue(
        []
      )
      spyOnSkipScanRequest.mockResolvedValue({} as any)

      // Act
      await consolidateScannerWorkLogs(startDate)

      // Assert
      expect(spyOnFindScanRequestsByQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          status: ScanRequestStatus.SCHEDULED,
          scheduledFor: {
            $gte: startDate,
            $lte: expect.any(Date)
          }
        })
      )
      expect(spyOnFindUserById).toHaveBeenCalledWith('worklog-janitor-id')
      expect(spyOnSkipScanRequest).toHaveBeenCalledWith(
        mockedScanRequests[0]._id.toString(),
        'Peek Skipped',
        mockedUser
      )
      expect(
        spyOnFindScanRequestsByDateGroupedByScannerAndCommunity
      ).toHaveBeenCalledWith(
        startDate,
        new Date(startDate.setHours(23, 59, 59, 999))
      )
      expect(
        spyOnFindScannerWorkLogByDateScannerAndCommunity
      ).not.toHaveBeenCalled()
      expect(spyOnCreateScannerWorkLog).not.toHaveBeenCalled()
    })

    it('should handle startDate with completed and skipped scan requests', async () => {
      // Arrange
      const startDate = new Date('2023-06-01')
      const groupedScanRequests = [
        {
          _id: {
            communityId: 'community-id-1',
            scannerId: 'scanner-id-1'
          },
          scanRequests: [
            mockScanRequest({ status: ScanRequestStatus.COMPLETED })
          ]
        },
        {
          _id: {
            communityId: 'community-id-2',
            scannerId: 'scanner-id-2'
          },
          scanRequests: [mockScanRequest({ status: ScanRequestStatus.SKIPPED })]
        }
      ]
      spyOnFindScanRequestsByQuery.mockResolvedValue([])
      spyOnFindUserById.mockResolvedValue(mockedUser)
      spyOnFindScanRequestsByDateGroupedByScannerAndCommunity.mockResolvedValue(
        groupedScanRequests
      )
      spyOnFindScannerWorkLogByDateScannerAndCommunity.mockResolvedValue(null)
      spyOnCreateScannerWorkLog.mockResolvedValue({} as any)

      // Act
      await consolidateScannerWorkLogs(startDate)

      // Assert
      expect(spyOnFindScanRequestsByQuery).toHaveBeenCalledWith({
        status: ScanRequestStatus.SCHEDULED,
        scheduledFor: {
          $gte: startDate,
          $lte: new Date(startDate.setHours(23, 59, 59, 999))
        }
      })
      expect(spyOnFindUserById).toHaveBeenCalledWith('worklog-janitor-id')
      expect(spyOnSkipScanRequest).not.toHaveBeenCalled()
      expect(
        spyOnFindScanRequestsByDateGroupedByScannerAndCommunity
      ).toHaveBeenCalledWith(
        startDate,
        new Date(startDate.setHours(23, 59, 59, 999))
      )
      expect(
        spyOnFindScannerWorkLogByDateScannerAndCommunity
      ).toHaveBeenCalledTimes(groupedScanRequests.length)
      expect(spyOnCreateScannerWorkLog).toHaveBeenCalledTimes(
        groupedScanRequests.length
      )
    })

    it('should handle non-existent workLogJanitorId', async () => {
      // Arrange
      const startDate = new Date('2023-06-01')
      spyOnFindScanRequestsByQuery.mockResolvedValue([])
      spyOnFindUserById.mockResolvedValue(null)

      // Act & Assert
      await expect(consolidateScannerWorkLogs(startDate)).rejects.toThrow(
        'Admin user not found.'
      )
      expect(spyOnFindScanRequestsByQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          status: ScanRequestStatus.SCHEDULED,
          scheduledFor: {
            $gte: startDate,
            $lte: expect.any(Date)
          }
        })
      )
      expect(spyOnSkipScanRequest).not.toHaveBeenCalled()
      expect(
        spyOnFindScanRequestsByDateGroupedByScannerAndCommunity
      ).not.toHaveBeenCalled()
      expect(
        spyOnFindScannerWorkLogByDateScannerAndCommunity
      ).not.toHaveBeenCalled()
      expect(spyOnCreateScannerWorkLog).not.toHaveBeenCalled()
    })

    it('should handle existing scanner work log', async () => {
      // Arrange
      const startDate = new Date('2023-06-01')
      const groupedScanRequests = [
        {
          _id: {
            communityId: 'community-id-1',
            scannerId: 'scanner-id-1'
          },
          scanRequests: [
            mockScanRequest({ status: ScanRequestStatus.COMPLETED })
          ]
        },
        {
          _id: {
            communityId: 'community-id-2',
            scannerId: 'scanner-id-2'
          },
          scanRequests: [mockScanRequest({ status: ScanRequestStatus.SKIPPED })]
        }
      ]
      const existingWorkLog = {
        _id: 'work-log-id',
        scanner: {
          _id: 'scanner-id-1'
        },
        community: {
          _id: 'community-id-1'
        },
        workLogDate: startDate,
        workingHours: 1,
        commuteTime: 1
      }
      spyOnFindScanRequestsByQuery.mockResolvedValue([])
      spyOnFindUserById.mockResolvedValue(mockedUser)
      spyOnFindScanRequestsByDateGroupedByScannerAndCommunity.mockResolvedValue(
        groupedScanRequests
      )
      spyOnFindScannerWorkLogByDateScannerAndCommunity.mockResolvedValue(
        existingWorkLog
      )
      spyOnUpdateScannerWorkLog.mockResolvedValue({} as any)

      // Act
      await consolidateScannerWorkLogs(startDate)

      // Assert
      expect(spyOnFindScanRequestsByQuery).toHaveBeenCalledWith(
        expect.objectContaining({
          status: ScanRequestStatus.SCHEDULED,
          scheduledFor: {
            $gte: startDate,
            $lte: expect.any(Date)
          }
        })
      )
      expect(spyOnFindUserById).toHaveBeenCalledWith('worklog-janitor-id')
      expect(spyOnSkipScanRequest).not.toHaveBeenCalled()
      expect(
        spyOnFindScanRequestsByDateGroupedByScannerAndCommunity
      ).toHaveBeenCalledWith(
        startDate,
        new Date(startDate.setHours(23, 59, 59, 999))
      )
      expect(
        spyOnFindScannerWorkLogByDateScannerAndCommunity
      ).toHaveBeenCalledTimes(groupedScanRequests.length)
      expect(spyOnUpdateScannerWorkLog).toHaveBeenCalledTimes(
        groupedScanRequests.length
      )
      expect(spyOnCreateScannerWorkLog).not.toHaveBeenCalled()
    })

    it('should handle multiple groups of scan requests', async () => {
      // Arrange
      const startDate = new Date('2023-06-01')
      const groupedScanRequests = [
        {
          _id: {
            communityId: 'community-id-1',
            scannerId: 'scanner-id-1'
          },
          scanRequests: [
            mockScanRequest({ status: ScanRequestStatus.COMPLETED })
          ]
        },
        {
          _id: {
            communityId: 'community-id-2',
            scannerId: 'scanner-id-2'
          },
          scanRequests: [mockScanRequest({ status: ScanRequestStatus.SKIPPED })]
        }
      ]
      spyOnFindScanRequestsByQuery.mockResolvedValue([])
      spyOnFindUserById.mockResolvedValue(mockedUser)
      spyOnFindScanRequestsByDateGroupedByScannerAndCommunity.mockResolvedValue(
        groupedScanRequests
      )
      spyOnFindScannerWorkLogByDateScannerAndCommunity.mockResolvedValue(null)
      spyOnCreateScannerWorkLog.mockResolvedValue({} as any)

      // Act
      await consolidateScannerWorkLogs(startDate)

      // Assert
      expect(
        spyOnFindScanRequestsByDateGroupedByScannerAndCommunity
      ).toHaveBeenCalledWith(startDate, expect.any(Date))
      expect(
        spyOnFindScannerWorkLogByDateScannerAndCommunity
      ).toHaveBeenCalledTimes(groupedScanRequests.length)
      expect(spyOnCreateScannerWorkLog).toHaveBeenCalledTimes(
        groupedScanRequests.length
      )
    })
  })

  describe('sendWorkLogRecapEmail', () => {
    const mockDate = new Date('2021-01-01')

    const spyOnSendMail = jest.spyOn(mail, 'sendMail')
    const spyOnFindUsers = jest.spyOn(userRepo, 'findUsers')
    const spyOnFindScannerWorkLogsByQuery = jest.spyOn(
      scannerWorkLogRepo,
      'findScannerWorkLogsByQuery'
    )
    const spyOnUpdateScannerWorkLog = jest.spyOn(
      scannerWorkLogRepo,
      'updateScannerWorkLog'
    )

    beforeEach(() => {
      jest.clearAllMocks()
    })

    it('should not proceed if no work logs are found', async () => {
      spyOnFindScannerWorkLogsByQuery.mockResolvedValue([])
      await sendWorkLogRecapEmail(mockDate)
      expect(spyOnSendMail).not.toHaveBeenCalled()
    })

    it('should not send emails if no users are found', async () => {
      spyOnFindScannerWorkLogsByQuery.mockResolvedValue([
        {
          _id: new ObjectId(),
          spaces: [],
          scanRequests: [],
          community: { _id: new ObjectId() },
          scanner: { _id: new ObjectId() },
          workLogDate: mockDate
        }
      ] as any)
      spyOnFindUsers.mockResolvedValue([])
      await sendWorkLogRecapEmail(mockDate)
      expect(spyOnSendMail).not.toHaveBeenCalled()
    })

    it('should send emails to all users when conditions are met', async () => {
      spyOnUpdateScannerWorkLog.mockResolvedValue({} as any)
      const spaceId = new ObjectId()
      const workLogId = new ObjectId()
      spyOnFindScannerWorkLogsByQuery.mockResolvedValue([
        {
          _id: workLogId,
          spaces: [
            {
              _id: spaceId,
              unit: 'Test Space',
              floorPlan: {
                name: 'Test Floor Plan'
              }
            }
          ],
          scanRequests: [
            {
              _id: new ObjectId(),
              status: 'COMPLETED',
              scheduledFor: mockDate,
              space: { _id: spaceId }
            }
          ],
          community: { _id: new ObjectId(), name: 'Test Community' },
          workLogDate: mockDate,
          scanner: {
            _id: new ObjectId(),
            name: 'Test User',
            email: '<EMAIL>'
          },
          photoshootReportSent: false,
          workingHours: 1,
          commuteTime: 1
        }
      ] as any)
      spyOnFindUsers.mockResolvedValue([
        { _id: new ObjectId(), email: '<EMAIL>', name: 'Test User' }
      ] as any)
      spyOnSendMail.mockResolvedValue({
        status: 'success',
        message: 'Email sent'
      })
      await sendWorkLogRecapEmail(mockDate)
      expect(spyOnSendMail).toHaveBeenCalledTimes(1)
      expect(spyOnSendMail).toHaveBeenCalledWith(
        '<EMAIL>',
        'ScanSessionReport',
        expect.objectContaining({
          userName: 'Test User',
          communityName: 'Test Community',
          capturedSpacesList: [
            {
              name: 'Test Space',
              floorPlan: 'Test Floor Plan',
              tourLink: expect.any(String)
            }
          ],
          skippedSpacesList: [],
          newPhotoshootRequestLink: expect.any(String)
        })
      )
      expect(spyOnUpdateScannerWorkLog).toHaveBeenCalledTimes(1)
      expect(spyOnUpdateScannerWorkLog).toHaveBeenCalledWith(
        { _id: workLogId.toString() },
        { photoshootReportSent: true }
      )
    })

    it('should handle spaces with empty floor plan', async () => {
      const workLogDate = new Date('2024-01-01')

      const mockSpace = {
        _id: 'space123',
        unit: 'Unit 101',
        token: 'test-token',
        floorPlan: null
      }

      const mockWorkLog = {
        _id: 'worklog123',
        scanner: {
          _id: 'scanner123',
          name: 'Test Scanner',
          email: '<EMAIL>'
        },
        community: { _id: 'community123', name: 'Test Community' },
        spaces: [mockSpace],
        scanRequests: [
          {
            _id: 'request123',
            status: ScanRequestStatus.COMPLETED,
            space: mockSpace
          }
        ],
        workLogDate
      }

      const mockUser = {
        _id: 'user123',
        email: '<EMAIL>',
        name: 'Test User',
        receiveScansSessionsReportEmail: true
      }

      // Mock repository calls
      jest
        .spyOn(scannerWorkLogRepo, 'findScannerWorkLogsByQuery')
        .mockResolvedValue([mockWorkLog] as any)
      jest.spyOn(userRepo, 'findUsers').mockResolvedValue([mockUser] as any)
      const sendMailSpy = jest
        .spyOn(mail, 'sendMail')
        .mockResolvedValue(undefined)
      const updateWorkLogSpy = jest
        .spyOn(scannerWorkLogRepo, 'updateScannerWorkLog')
        .mockResolvedValue(mockWorkLog as any)

      // Execute
      await sendWorkLogRecapEmail(workLogDate)

      // Verify
      expect(sendMailSpy).toHaveBeenCalledWith(
        mockUser.email,
        mail.KLAVIYO_EVENTS.SCANS_SESSIONS_REPORT,
        expect.objectContaining({
          capturedSpacesList: [
            {
              name: mockSpace.unit,
              floorPlan: '',
              tourLink: expect.stringContaining(mockSpace.token)
            }
          ],
          skippedSpacesList: [],
          communityName: mockWorkLog.community.name
        })
      )

      expect(updateWorkLogSpy).toHaveBeenCalledWith(
        { _id: mockWorkLog._id },
        { photoshootReportSent: true }
      )
    })
  })
})
