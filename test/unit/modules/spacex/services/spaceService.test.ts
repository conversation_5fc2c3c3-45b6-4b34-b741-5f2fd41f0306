import { flattenObjectKeys } from '@core/util'
import * as externalLinkRepository from '@modules/communities/repositories/externalLink'
import * as buildingService from '@modules/communities/services/building'
import * as communityService from '@modules/communities/services/community'
import * as scanRequestService from '@modules/communities/services/scanRequest'
import * as service from '@modules/communities/services/space'
import { Address } from '@modules/communities/types/address'
import { Building } from '@modules/communities/types/building'
import { Community } from '@modules/communities/types/community'
import {
  ExternalLinkName,
  ExternalLinkService
} from '@modules/communities/types/externalLink'
import { Id, generateId } from '@modules/communities/types/id'
import { Space } from '@modules/communities/types/space'
import { SpaceType } from '@modules/communities/types/spaceType'
import * as hereService from '@modules/here/services/lookupAddress'
import * as accessDeviceService from '@modules/sgt/services/accessDevice'
import * as spaceEvents from '@modules/spacex/events/spaceEvents'
import { spaceRepositoryMongoDb } from '@modules/spacex/repositories/spaceRepository'
import { vacancyRepositoryMongoDb } from '@modules/spacex/repositories/vacancyRepository'
import * as spaceService from '@modules/spacex/services/spaceService'
import * as virtualTourService from '@modules/spacex/services/virtualTourService'
import {
  mockAddress,
  mockBuilding,
  mockCommunity,
  mockSpace
} from '@test/helpers/community.helper'
import * as scanRequestRepository from '@modules/communities/repositories/scanRequest'
import { ScanRequest } from '@modules/communities/types/scanRequest'
import * as logger from '@core/log'

jest.mock('@core/db', () => ({
  connect: jest.fn()
}))

jest.mock('@core/db', () => ({
  connect: jest.fn()
}))

describe('SpaceX', () => {
  afterEach(() => {
    jest.resetAllMocks()
    jest.clearAllMocks()
  })

  let createSpy: jest.SpyInstance
  let updateSpy: jest.SpyInstance
  let spaceEventSpy: jest.SpyInstance

  let spaceId: string | Id
  let buildingId: string | Id
  let communityId: string | Id

  let address: Address
  let building: Building
  let community: Community
  let space: Space

  beforeEach(() => {
    spaceId = generateId()
    buildingId = generateId()
    communityId = generateId()

    address = mockAddress()
    building = mockBuilding({ _id: buildingId, address })
    community = mockCommunity({ _id: communityId })

    space = mockSpace({
      _id: spaceId,
      building: { ...building, address },
      community: { ...community, _id: communityId }
    })

    jest
      .spyOn(vacancyRepositoryMongoDb, 'findOne')
      .mockResolvedValueOnce(undefined)
    jest
      .spyOn(communityService, 'findCommunityById')
      .mockResolvedValueOnce(community)
    jest
      .spyOn(buildingService, 'findBuildingById')
      .mockResolvedValueOnce(building)
    jest.spyOn(spaceRepositoryMongoDb, 'create').mockResolvedValue(undefined)
    jest.spyOn(hereService, 'lookupAddress').mockResolvedValue({
      items: [
        {
          position: {
            lat: 1,
            lng: 1
          }
        }
      ]
    })

    jest
      .spyOn(externalLinkRepository, 'addOrUpdateExternalLinkBy')
      .mockResolvedValue(undefined)

    spaceEventSpy = jest
      .spyOn(spaceEvents, 'publishSpaceEvent')
      .mockResolvedValue(undefined)
  })

  describe('Service', () => {
    describe('#addOrUpdateSpaceById', () => {
      describe('WHEN space exists', () => {
        beforeEach(() => {
          createSpy = jest
            .spyOn(spaceService, 'create')
            .mockResolvedValueOnce(undefined)
          updateSpy = jest
            .spyOn(spaceService, 'update')
            .mockResolvedValueOnce(undefined)
        })
        it('should call update', async () => {
          await spaceService.addOrUpdateSpaceById(spaceId.toString(), space)
          expect(createSpy).not.toHaveBeenCalled()
          expect(updateSpy).toHaveBeenCalled()
        })
      })

      describe('WHEN space does not exist', () => {
        beforeEach(() => {
          createSpy = jest
            .spyOn(spaceService, 'create')
            .mockResolvedValueOnce(undefined)
          updateSpy = jest
            .spyOn(spaceService, 'update')
            .mockResolvedValueOnce(undefined)
        })
        it('should call create', async () => {
          await spaceService.addOrUpdateSpaceById(undefined, space)
          expect(createSpy).toHaveBeenCalled()
          expect(updateSpy).not.toHaveBeenCalled()
        })
      })
    })

    describe('#create', () => {
      beforeEach(() => {
        createSpy?.mockRestore()
      })

      describe('WHEN has rentPrices', () => {
        describe('AND has no externalLinks', () => {
          describe('AND has building', () => {
            describe('AND has address', () => {
              it('should create space', async () => {
                const createdSpaceId = generateId()

                jest
                  .spyOn(spaceRepositoryMongoDb, 'create')
                  .mockResolvedValueOnce({
                    ...space,
                    _id: createdSpaceId
                  })

                await spaceService.create(
                  space,
                  communityId.toString(),
                  building
                )
                expect(communityService.findCommunityById).toHaveBeenCalledWith(
                  communityId.toString()
                )
                expect(buildingService.findBuildingById).not.toHaveBeenCalled()
                expect(spaceRepositoryMongoDb.create).toHaveBeenCalledWith(
                  flattenObjectKeys(space)
                )
                expect(spaceEventSpy).toHaveBeenCalledWith(
                  createdSpaceId.toString(),
                  undefined,
                  undefined,
                  spaceEvents.SpaceEventType.CREATED
                )
              })
            })

            describe('AND publishSpaceEvent fail', () => {
              it('should create space and return correctly', async () => {
                const spy = jest
                  .spyOn(spaceEvents, 'publishSpaceEvent')
                  .mockRejectedValueOnce(new Error('error to publish'))
                jest
                  .spyOn(spaceRepositoryMongoDb, 'create')
                  .mockResolvedValueOnce(space)

                const result = await spaceService.create(
                  space,
                  communityId.toString(),
                  building
                )

                expect(result).toEqual(space)
                expect(spy).toHaveBeenCalledTimes(1)
              })
            })

            describe('AND has no address', () => {
              it('should create space', async () => {
                jest
                  .spyOn(spaceRepositoryMongoDb, 'create')
                  .mockResolvedValueOnce(space)

                const spaceCommunity = {
                  _id: community._id,
                  name: community.name,
                  organization: community.organization,
                  ...community.communityInfo,
                  ...community.communityStyle,
                  showContactForm: community.showContactForm,
                  canBypassContactForm: community.canBypassContactForm,
                  displayPriceField: community.displayPriceField,
                  isActive: community.isActive,
                  showAddress: community.showAddress
                }

                space.community = spaceCommunity

                const address = Object.assign({}, space.address)

                await spaceService.create(
                  { ...space, building: { ...building, address: undefined } },
                  communityId.toString(),
                  { ...building, address: undefined }
                )
                expect(communityService.findCommunityById).toHaveBeenCalledWith(
                  communityId.toString()
                )
                expect(spaceRepositoryMongoDb.create).toHaveBeenCalledWith(
                  flattenObjectKeys({
                    ...space,
                    building: {
                      _id: building._id,
                      address,
                      communityId: building.communityId,
                      alternativeName: building.alternativeName,
                      spaceIds: building.spaceIds,
                      location: {
                        type: 'Point',
                        coordinates: [address.longitude, address.latitude]
                      }
                    },
                    community: spaceCommunity,
                    displayPrice: space.rentPrices[0].price
                  })
                )
                expect(spaceEventSpy).toBeCalledTimes(1)
              })
            })

            describe('AND address has no latitude and longitude', () => {
              it('should create space', async () => {
                const currentBuilding = {
                  ...building,
                  address: {
                    ...address,
                    latitude: undefined,
                    longitude: undefined
                  }
                }

                const spaceCommunity = {
                  _id: community._id,
                  name: community.name,
                  organization: community.organization,
                  ...community.communityInfo,
                  ...community.communityStyle,
                  showContactForm: community.showContactForm,
                  canBypassContactForm: community.canBypassContactForm,
                  displayPriceField: community.displayPriceField,
                  isActive: community.isActive,
                  showAddress: community.showAddress
                }

                await spaceService.create(
                  {
                    ...space,
                    building: currentBuilding
                  },
                  communityId.toString(),
                  currentBuilding
                )
                expect(communityService.findCommunityById).toHaveBeenCalledWith(
                  communityId.toString()
                )
                expect(buildingService.findBuildingById).not.toHaveBeenCalled()

                expect(spaceRepositoryMongoDb.create).toHaveBeenCalledWith(
                  flattenObjectKeys({
                    ...space,
                    community: spaceCommunity,
                    displayPrice: space.rentPrices[0].price,
                    building: {
                      ...currentBuilding,
                      address: currentBuilding.address
                    }
                  })
                )
              })
            })
          })
        })

        describe('AND has externalLinks', () => {
          it('should create space and create external links', async () => {
            jest
              .spyOn(spaceRepositoryMongoDb, 'create')
              .mockResolvedValueOnce(space)

            const externalLinks = [
              {
                externalId: '123',
                externalName: ExternalLinkName.ThirdPartyId,
                service: ExternalLinkService.Entrata
              }
            ]

            await spaceService.create(
              space,
              communityId.toString(),
              building,
              externalLinks as any
            )

            expect(
              externalLinkRepository.addOrUpdateExternalLinkBy
            ).toHaveReturnedTimes(1)
            expect(
              externalLinkRepository.addOrUpdateExternalLinkBy
            ).toHaveBeenCalledWith(
              {
                externalId: '123',
                externalName: 'ThirdPartyID',
                service: 'entrata',
                objectId: spaceId.toString(),
                communityId: communityId.toString(),
                collectionPath: 'spaces'
              },
              {
                externalId: '123',
                externalName: 'ThirdPartyID',
                service: 'entrata',
                objectId: spaceId.toString(),
                communityId: communityId.toString(),
                collectionPath: 'spaces'
              }
            )
          })
        })
      })

      describe('When provided building without _id', () => {
        beforeEach(() => {
          jest.clearAllMocks()
        })
        describe('And there is already a building with the same placeId', () => {
          describe('And the addresses match', () => {
            it('Should link the existing building to the space', async () => {
              const existingAddress = mockAddress({ placeId: '123' })
              const mockExistingBuilding = mockBuilding({
                address: existingAddress,
                name: 'test',
                location: {
                  type: 'Point',
                  coordinates: [
                    existingAddress.longitude,
                    existingAddress.latitude
                  ]
                }
              })
              delete mockExistingBuilding.communityId
              delete mockExistingBuilding.spaceIds
              const findOneBuildingMock = jest
                .spyOn(buildingService, 'findOneBuilding')
                .mockResolvedValueOnce(mockBuilding(mockExistingBuilding))

              const buildingPayload = {
                address: { ...existingAddress, placeId: '123' },
                name: 'test'
              } as any

              const spaceCommunity = {
                _id: community._id,
                name: community.name,
                organization: community.organization,
                ...community.communityInfo,
                ...community.communityStyle,
                showContactForm: community.showContactForm,
                canBypassContactForm: community.canBypassContactForm,
                displayPriceField: community.displayPriceField,
                isActive: community.isActive,
                showAddress: community.showAddress
              }

              space.community = spaceCommunity

              await spaceService.create(
                {
                  ...space,
                  building: buildingPayload
                },
                communityId.toString(),
                buildingPayload
              )

              expect(findOneBuildingMock).toHaveBeenCalledWith({
                'address.placeId': '123',
                communityId: communityId.toString()
              })

              const createSpaceCalledPayload = (
                spaceRepositoryMongoDb.create as unknown as jest.SpyInstance
              ).mock.calls[0][0]

              expect(createSpaceCalledPayload).toMatchObject(
                flattenObjectKeys({
                  unit: space.unit,
                  building: {
                    ...mockExistingBuilding,
                    address: mockExistingBuilding.address,
                    location: {
                      type: 'Point',
                      coordinates: [
                        mockExistingBuilding.address.longitude,
                        mockExistingBuilding.address.latitude
                      ]
                    }
                  }
                })
              )
            })
          })
          describe('And the addresses DONT match', () => {
            it('Should set the building_id in the space but keep the incoming address', async () => {
              const existingAddress = mockAddress({ placeId: '123' })
              const mockExistingBuilding = mockBuilding({
                address: existingAddress,
                name: 'test'
              })
              delete mockExistingBuilding.communityId
              delete mockExistingBuilding.spaceIds

              const findOneBuildingMock = jest
                .spyOn(buildingService, 'findOneBuilding')
                .mockResolvedValueOnce(mockBuilding(mockExistingBuilding))

              const buildingPayload = {
                address: {
                  ...existingAddress,
                  placeId: '123',
                  street: '123'
                },
                name: 'test'
              } as any

              const spaceCommunity = {
                _id: community._id,
                name: community.name,
                organization: community.organization,
                ...community.communityInfo,
                ...community.communityStyle,
                showContactForm: community.showContactForm,
                canBypassContactForm: community.canBypassContactForm,
                displayPriceField: community.displayPriceField,
                isActive: community.isActive,
                showAddress: community.showAddress
              }

              space.community = spaceCommunity

              await spaceService.create(
                {
                  ...space,
                  building: buildingPayload
                },
                communityId.toString(),
                buildingPayload
              )

              expect(findOneBuildingMock).toHaveBeenCalledWith({
                'address.placeId': '123',
                communityId: communityId.toString()
              })

              const createSpaceCalledPayload = (
                spaceRepositoryMongoDb.create as unknown as jest.SpyInstance
              ).mock.calls[0][0]

              expect(createSpaceCalledPayload).toMatchObject(
                flattenObjectKeys({
                  unit: space.unit,
                  building: {
                    ...mockExistingBuilding,
                    _id: mockExistingBuilding._id,
                    name: mockExistingBuilding.name,
                    alternativeName: mockExistingBuilding.alternativeName,
                    location: {
                      type: 'Point',
                      coordinates: [
                        mockExistingBuilding.address.longitude,
                        mockExistingBuilding.address.latitude
                      ]
                    }
                  }
                })
              )
            })
          })
        })
        describe('And there is no buildings with the same placeId', () => {
          it('Should create a new building and link it to the space', async () => {
            const findOneBuildingMock = jest
              .spyOn(buildingService, 'findOneBuilding')
              .mockResolvedValueOnce(null)

            const createBuildingMock = jest
              .spyOn(buildingService, 'createBuilding')
              .mockResolvedValueOnce(null)

            const addressPayload = {
              street: '123',
              placeId: '123',
              city: 'test',
              state: 'test',
              postalCode: '123'
            }
            const buildingPayload = {
              address: addressPayload,
              name: 'test'
            } as any

            const spaceCommunity = {
              _id: community._id,
              name: community.name,
              organization: community.organization,
              ...community.communityInfo,
              ...community.communityStyle,
              showContactForm: community.showContactForm,
              canBypassContactForm: community.canBypassContactForm,
              displayPriceField: community.displayPriceField,
              isActive: community.isActive,
              showAddress: community.showAddress
            }

            space.community = spaceCommunity

            await spaceService.create(
              {
                ...space,
                building: buildingPayload
              },
              communityId.toString(),
              buildingPayload
            )

            expect(findOneBuildingMock).toHaveBeenCalledWith({
              'address.placeId': '123',
              communityId: communityId.toString()
            })

            expect(createBuildingMock).toHaveBeenCalledWith({
              ...buildingPayload,
              communityId: communityId.toString(),
              location: {
                type: 'Point',
                coordinates: [
                  buildingPayload.address.longitude,
                  buildingPayload.address.latitude
                ]
              }
            })

            const createSpaceCalledPayload = (
              spaceRepositoryMongoDb.create as unknown as jest.SpyInstance
            ).mock.calls[0][0]

            expect(createSpaceCalledPayload).toMatchObject(
              flattenObjectKeys({
                unit: space.unit,
                building: buildingPayload
              })
            )
          })
        })
        describe('And the provided building does not have placeId', () => {
          it('Should not set the building_id in the space', async () => {
            const existingAddress = mockAddress({ placeId: '123' })
            const mockExistingBuilding = mockBuilding({
              address: existingAddress,
              name: 'test'
            })
            delete mockExistingBuilding.communityId
            delete mockExistingBuilding.spaceIds

            const findOneBuildingMock = jest
              .spyOn(buildingService, 'findOneBuilding')
              .mockResolvedValueOnce(mockBuilding(mockExistingBuilding))

            const buildingPayload = {
              address: { street: '123' },
              name: 'test'
            } as any

            const spaceCommunity = {
              _id: community._id,
              name: community.name,
              organization: community.organization,
              ...community.communityInfo,
              ...community.communityStyle,
              showContactForm: community.showContactForm,
              canBypassContactForm: community.canBypassContactForm,
              displayPriceField: community.displayPriceField,
              isActive: community.isActive,
              showAddress: community.showAddress
            }

            space.community = spaceCommunity

            await spaceService.create(
              {
                ...space,
                building: buildingPayload
              },
              communityId.toString(),
              buildingPayload
            )

            expect(findOneBuildingMock).not.toHaveBeenCalled()

            const createSpaceCalledPayload = (
              spaceRepositoryMongoDb.create as unknown as jest.SpyInstance
            ).mock.calls[0][0]

            expect(createSpaceCalledPayload).toMatchObject(
              flattenObjectKeys({
                unit: space.unit,
                building: buildingPayload
              })
            )
          })
        })
      })

      describe('WHEN isVisible is false', () => {
        it('should set sgtEnabled to false', async () => {
          const createdSpaceId = generateId()
          const spaceWithVisibility = {
            ...space,
            isVisible: false
          }

          jest.spyOn(spaceRepositoryMongoDb, 'create').mockResolvedValueOnce({
            ...spaceWithVisibility,
            _id: createdSpaceId
          })

          await spaceService.create(
            spaceWithVisibility,
            communityId.toString(),
            building
          )

          expect(spaceRepositoryMongoDb.create).toHaveBeenCalledWith(
            expect.objectContaining({
              isVisible: false,
              sgtEnabled: false
            })
          )
        })
      })
    })

    describe('#update', () => {
      beforeEach(() => {
        updateSpy?.mockRestore()
      })

      describe('WHEN space does not exist', () => {
        it('should raise an error', async () => {
          const otherSpaceId = generateId()

          jest
            .spyOn(spaceRepositoryMongoDb, 'findById')
            .mockResolvedValueOnce(undefined)

          await expect(
            spaceService.update(otherSpaceId.toString(), space)
          ).rejects.toThrowError()
        })
      })
      describe('WHEN space exists', () => {
        describe('AND community exists', () => {
          describe('AND nodes does not exist', () => {
            describe('AND access devices does not exist', () => {
              describe('AND rent prices does not exist', () => {
                describe('AND building does not exist', () => {
                  it('should update space', async () => {
                    const partialSpace = {
                      bedrooms: 2,
                      bathrooms: 1
                    }

                    jest
                      .spyOn(spaceRepositoryMongoDb, 'findById')
                      .mockResolvedValueOnce({
                        ...partialSpace,
                        _id: space._id
                      } as Space)

                    jest
                      .spyOn(spaceRepositoryMongoDb, 'update')
                      .mockResolvedValueOnce(undefined)

                    await spaceService.update(spaceId.toString(), partialSpace)

                    expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                      spaceId.toString(),
                      flattenObjectKeys(partialSpace),
                      undefined
                    )
                    expect(spaceEventSpy).toBeCalledTimes(1)
                  })
                })
                describe('AND building exists', () => {
                  it('should update space', async () => {
                    const partialSpace = {
                      bedrooms: 2,
                      bathrooms: 1
                    }

                    jest
                      .spyOn(spaceRepositoryMongoDb, 'findById')
                      .mockResolvedValueOnce({
                        ...partialSpace,
                        building,
                        _id: space._id
                      } as any)

                    jest
                      .spyOn(spaceRepositoryMongoDb, 'update')
                      .mockResolvedValueOnce(undefined)

                    jest
                      .spyOn(buildingService, 'findBuildingById')
                      .mockResolvedValueOnce({
                        ...building,
                        address
                      })

                    await spaceService.update(
                      spaceId.toString(),
                      partialSpace,
                      building
                    )

                    const expectedOptions = undefined
                    const expectedPayload = flattenObjectKeys({
                      ...partialSpace,
                      building: {
                        ...building,
                        address,
                        location: {
                          type: 'Point',
                          coordinates: [
                            building.address.longitude,
                            building.address.latitude
                          ]
                        }
                      }
                    })

                    expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                      spaceId.toString(),
                      expectedPayload,
                      expectedOptions
                    )
                  })
                })
              })

              describe('AND rent prices exists', () => {
                it('should update space', async () => {
                  const partialSpace = {
                    bedrooms: 2,
                    bathrooms: 1,
                    rentPrices: [
                      {
                        termInMonths: 12,
                        price: 1000,
                        enabled: true
                      }
                    ]
                  }

                  jest
                    .spyOn(spaceRepositoryMongoDb, 'findById')
                    .mockResolvedValueOnce({
                      ...partialSpace,
                      _id: space._id
                    } as Space)

                  jest
                    .spyOn(spaceRepositoryMongoDb, 'update')
                    .mockResolvedValueOnce(undefined)

                  await spaceService.update(spaceId.toString(), partialSpace)

                  const expectedOptions = undefined
                  expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                    spaceId.toString(),
                    flattenObjectKeys(partialSpace),
                    expectedOptions
                  )
                  expect(partialSpace['displayPrice']).toEqual(1000)
                })
              })
            })

            describe('AND access devices exists', () => {
              it('should update space', async () => {
                const partialSpace = {
                  bedrooms: 2,
                  bathrooms: 1,
                  accessDevice: {
                    devices: ['1', '2'],
                    tourInstructions: 'test'
                  }
                }

                jest
                  .spyOn(spaceRepositoryMongoDb, 'findById')
                  .mockResolvedValueOnce({
                    ...partialSpace,
                    _id: space._id
                  } as any)

                jest
                  .spyOn(spaceRepositoryMongoDb, 'update')
                  .mockResolvedValueOnce(undefined)

                jest
                  .spyOn(accessDeviceService, 'linkDevicesInOrder')
                  .mockResolvedValue({
                    _id: '123',
                    accessDeviceId: '1'
                  } as any)

                await spaceService.update(
                  spaceId.toString(),
                  partialSpace as any
                )

                const expectedOptions = undefined

                const { accessDevice, ...rest } = partialSpace

                expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                  spaceId.toString(),
                  {
                    ...flattenObjectKeys(rest),
                    accessDevice
                  },
                  expectedOptions
                )

                expect(partialSpace['accessDevice']['devices']).toEqual([
                  '1',
                  '2'
                ])
                expect(
                  partialSpace['accessDevice']['tourInstructions']
                ).toEqual('test')
              })
            })
          })

          describe('AND nodes exists', () => {
            describe('AND process image is undefined', () => {
              it('should update space', async () => {
                const nodeLinkNodeId = generateId()
                const node = {
                  captureCount: 0,
                  label: 'BEDROOM CLOSET',
                  nodeLinks: [
                    {
                      label: 'KITCHEN',
                      node: nodeLinkNodeId.toString(),
                      position: {
                        x: '-0.3256226181983948',
                        y: '0',
                        z: '7.977580070495605'
                      },
                      rotation: {
                        pitch: '0',
                        yaw: '2.337356805801392'
                      }
                    }
                  ],
                  photo: {
                    url: '65afa11364e6e0344a5866ac/BEDROOM CLOSET.jpg'
                  },
                  rotation: {
                    pitch: 0,
                    roll: 0,
                    yaw: 0
                  }
                }
                const partialSpace = {
                  bedrooms: 2,
                  bathrooms: 1,
                  nodes: [node]
                }

                jest
                  .spyOn(spaceRepositoryMongoDb, 'findById')
                  .mockResolvedValueOnce({
                    ...partialSpace,
                    _id: space._id
                  } as any)

                jest
                  .spyOn(spaceRepositoryMongoDb, 'update')
                  .mockResolvedValueOnce(undefined)

                await spaceService.update(
                  spaceId.toString(),
                  partialSpace as any
                )

                const expectedOptions = undefined
                expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                  spaceId.toString(),
                  flattenObjectKeys(partialSpace),
                  expectedOptions
                )

                expect(partialSpace.nodes[0]['_id']).not.toBeUndefined()
                expect(partialSpace.nodes[0]['position']).toBeUndefined()
                expect(
                  partialSpace.nodes[0]['nodeLinks'][0]['rotation']
                ).toStrictEqual({
                  pitch: '0',
                  yaw: '2.337356805801392'
                })
              })
            })

            describe('AND process image is true', () => {
              it('should update space', async () => {
                const nodeLinkNodeId = generateId()
                const node = {
                  captureCount: 0,
                  label: 'BEDROOM CLOSET',
                  nodeLinks: [
                    {
                      label: 'KITCHEN',
                      node: nodeLinkNodeId.toString(),
                      position: {
                        x: '-0.3256226181983948',
                        y: '0',
                        z: '7.977580070495605'
                      },
                      rotation: {
                        pitch: '0',
                        yaw: '2.337356805801392'
                      }
                    }
                  ],
                  photo: {
                    url: '65afa11364e6e0344a5866ac/BEDROOM CLOSET.jpg'
                  },
                  rotation: {
                    pitch: 0,
                    roll: 0,
                    yaw: 0
                  }
                }
                const partialSpace = {
                  bedrooms: 2,
                  bathrooms: 1,
                  nodes: [node]
                }

                jest
                  .spyOn(spaceRepositoryMongoDb, 'findById')
                  .mockResolvedValueOnce({
                    ...partialSpace,
                    community: { _id: communityId },
                    _id: space._id
                  } as any)

                jest
                  .spyOn(spaceRepositoryMongoDb, 'update')
                  .mockResolvedValueOnce(undefined)

                jest
                  .spyOn(service, 'sendScanEmails')
                  .mockResolvedValueOnce(undefined)

                const date = '2021-01-01T00:00:00.000Z'
                jest
                  .spyOn(virtualTourService, 'processVirtualTour')
                  .mockResolvedValueOnce({
                    isComplete: true,
                    nodes: [node as any],
                    isMultiRes: true,
                    isVisible: true,
                    tourCapturedDate: date as any,
                    tourLastUpdatedDate: date as any
                  })
                jest
                  .spyOn(scanRequestService, 'markScanRequestAsUploaded')
                  .mockResolvedValueOnce({} as any)

                await spaceService.update(
                  spaceId.toString(),
                  partialSpace as any,
                  undefined,
                  true
                )

                const expectedOptions = undefined
                expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                  spaceId.toString(),
                  flattenObjectKeys({
                    ...partialSpace,
                    isComplete: true,
                    isMultiRes: true,
                    isVisible: true,
                    tourCapturedDate: date,
                    tourLastUpdatedDate: date
                  }),
                  expectedOptions
                )
                expect(partialSpace.nodes[0]['_id']).not.toBeUndefined()
                expect(partialSpace.nodes[0]['position']).toBeUndefined()
                expect(
                  partialSpace.nodes[0]['nodeLinks'][0]['rotation']
                ).toStrictEqual({
                  pitch: '0',
                  yaw: '2.337356805801392'
                })
              })
              it('should complete the scan request', async () => {
                const nodeLinkNodeId = generateId()
                const node = {
                  captureCount: 0,
                  label: 'BEDROOM CLOSET',
                  nodeLinks: [
                    {
                      label: 'KITCHEN',
                      node: nodeLinkNodeId.toString(),
                      position: {
                        x: '-0.3256226181983948',
                        y: '0',
                        z: '7.977580070495605'
                      },
                      rotation: {
                        pitch: '0',
                        yaw: '2.337356805801392'
                      }
                    }
                  ],
                  photo: {
                    url: '65afa11364e6e0344a5866ac/BEDROOM CLOSET.jpg'
                  },
                  rotation: {
                    pitch: 0,
                    roll: 0,
                    yaw: 0
                  }
                }
                const partialSpace = {
                  bedrooms: 2,
                  bathrooms: 1,
                  nodes: [node]
                }

                jest
                  .spyOn(spaceRepositoryMongoDb, 'findById')
                  .mockResolvedValueOnce({
                    ...partialSpace,
                    community: { _id: communityId },
                    _id: space._id
                  } as any)

                jest
                  .spyOn(spaceRepositoryMongoDb, 'update')
                  .mockResolvedValueOnce(undefined)

                jest
                  .spyOn(service, 'sendScanEmails')
                  .mockResolvedValueOnce(undefined)

                const date = '2021-01-01T00:00:00.000Z'
                jest
                  .spyOn(virtualTourService, 'processVirtualTour')
                  .mockResolvedValueOnce({
                    isComplete: true,
                    nodes: [node as any],
                    isMultiRes: true,
                    isVisible: true,
                    tourCapturedDate: date as any,
                    tourLastUpdatedDate: date as any
                  })

                jest
                  .spyOn(scanRequestService, 'markScanRequestAsUploaded')
                  .mockResolvedValueOnce({} as any)

                await spaceService.update(
                  spaceId.toString(),
                  partialSpace as any,
                  undefined,
                  true,
                  undefined,
                  undefined,
                  new Date()
                )

                const expectedOptions = undefined
                expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                  spaceId.toString(),
                  flattenObjectKeys({
                    ...partialSpace,
                    isComplete: true,
                    isMultiRes: true,
                    isVisible: true,
                    tourCapturedDate: date,
                    tourLastUpdatedDate: date
                  }),
                  expectedOptions
                )
                expect(
                  scanRequestService.markScanRequestAsUploaded
                ).toHaveBeenCalledWith(spaceId.toString(), expect.any(Date))
                expect(partialSpace.nodes[0]['_id']).not.toBeUndefined()
                expect(partialSpace.nodes[0]['position']).toBeUndefined()
                expect(
                  partialSpace.nodes[0]['nodeLinks'][0]['rotation']
                ).toStrictEqual({
                  pitch: '0',
                  yaw: '2.337356805801392'
                })
              })
            })
          })

          describe('And has external links', () => {
            it('should create space and create external links', async () => {
              const partialSpace = {
                bedrooms: 2,
                bathrooms: 1,
                rentPrices: [
                  {
                    termInMonths: 12,
                    price: 1000,
                    enabled: true
                  }
                ]
              }

              jest
                .spyOn(spaceRepositoryMongoDb, 'findById')
                .mockResolvedValueOnce({
                  ...partialSpace,
                  _id: space._id
                } as Space)

              jest
                .spyOn(spaceRepositoryMongoDb, 'update')
                .mockResolvedValueOnce(undefined)

              const externalLinks = [
                {
                  externalId: '123',
                  externalName: ExternalLinkName.ThirdPartyId,
                  service: ExternalLinkService.Entrata
                }
              ]

              await spaceService.update(
                spaceId.toString(),
                partialSpace,
                null,
                false,
                externalLinks
              )

              const expectedOptions = undefined
              expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                spaceId.toString(),
                flattenObjectKeys(partialSpace),
                expectedOptions
              )
              expect(partialSpace['displayPrice']).toEqual(1000)

              jest
                .spyOn(spaceRepositoryMongoDb, 'create')
                .mockResolvedValueOnce(space)

              expect(
                externalLinkRepository.addOrUpdateExternalLinkBy
              ).toHaveReturnedTimes(1)
              expect(
                externalLinkRepository.addOrUpdateExternalLinkBy
              ).toHaveBeenCalledWith(
                {
                  externalId: '123',
                  externalName: 'ThirdPartyID',
                  service: 'entrata',
                  objectId: spaceId.toString(),
                  communityId: communityId.toString(),
                  collectionPath: 'spaces'
                },
                {
                  externalId: '123',
                  externalName: 'ThirdPartyID',
                  service: 'entrata',
                  objectId: spaceId.toString(),
                  communityId: communityId.toString(),
                  collectionPath: 'spaces'
                }
              )
            })
          })

          describe('And space is isComplete', () => {
            describe('And scheduledFor is passed', () => {
              it('should call markScanRequestAsUploaded', async () => {
                const partialSpace = { isComplete: true }
                const scheduledFor = new Date()
                jest
                  .spyOn(spaceRepositoryMongoDb, 'findById')
                  .mockResolvedValueOnce({
                    ...partialSpace,
                    _id: space._id
                  } as Space)

                jest
                  .spyOn(spaceRepositoryMongoDb, 'update')
                  .mockResolvedValueOnce(undefined)

                const spyMarScanRequestAsUploaded = jest
                  .spyOn(scanRequestService, 'markScanRequestAsUploaded')
                  .mockResolvedValueOnce({} as any)

                const externalLinks = [
                  {
                    externalId: '123',
                    externalName: ExternalLinkName.ThirdPartyId,
                    service: ExternalLinkService.Entrata
                  }
                ]

                await spaceService.update(
                  spaceId.toString(),
                  partialSpace,
                  null,
                  false,
                  externalLinks,
                  null,
                  scheduledFor
                )

                const expectedOptions = null
                expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                  spaceId.toString(),
                  flattenObjectKeys(partialSpace),
                  expectedOptions
                )

                jest
                  .spyOn(spaceRepositoryMongoDb, 'create')
                  .mockResolvedValueOnce(space)

                expect(
                  externalLinkRepository.addOrUpdateExternalLinkBy
                ).toHaveReturnedTimes(1)
                expect(
                  externalLinkRepository.addOrUpdateExternalLinkBy
                ).toHaveBeenCalledWith(
                  {
                    externalId: '123',
                    externalName: 'ThirdPartyID',
                    service: 'entrata',
                    objectId: spaceId.toString(),
                    communityId: communityId.toString(),
                    collectionPath: 'spaces'
                  },
                  {
                    externalId: '123',
                    externalName: 'ThirdPartyID',
                    service: 'entrata',
                    objectId: spaceId.toString(),
                    communityId: communityId.toString(),
                    collectionPath: 'spaces'
                  }
                )
                expect(spyMarScanRequestAsUploaded).toHaveBeenCalledTimes(1)
                expect(spyMarScanRequestAsUploaded).toHaveBeenCalledWith(
                  space._id.toString(),
                  scheduledFor
                )
              })
            })

            describe('And scheduledFor is not passed', () => {
              it('should not call markScanRequestAsUploaded', async () => {
                const partialSpace = { isComplete: true }
                const scheduledFor = undefined
                jest
                  .spyOn(spaceRepositoryMongoDb, 'findById')
                  .mockResolvedValueOnce({
                    ...partialSpace,
                    _id: space._id
                  } as Space)

                jest
                  .spyOn(spaceRepositoryMongoDb, 'update')
                  .mockResolvedValueOnce(undefined)

                const spyMarScanRequestAsUploaded = jest
                  .spyOn(scanRequestService, 'markScanRequestAsUploaded')
                  .mockResolvedValueOnce({} as any)

                const externalLinks = [
                  {
                    externalId: '123',
                    externalName: ExternalLinkName.ThirdPartyId,
                    service: ExternalLinkService.Entrata
                  }
                ]

                await spaceService.update(
                  spaceId.toString(),
                  partialSpace,
                  null,
                  false,
                  externalLinks,
                  null,
                  scheduledFor
                )

                const expectedOptions = null
                expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                  spaceId.toString(),
                  flattenObjectKeys(partialSpace),
                  expectedOptions
                )

                jest
                  .spyOn(spaceRepositoryMongoDb, 'create')
                  .mockResolvedValueOnce(space)

                expect(
                  externalLinkRepository.addOrUpdateExternalLinkBy
                ).toHaveReturnedTimes(1)
                expect(
                  externalLinkRepository.addOrUpdateExternalLinkBy
                ).toHaveBeenCalledWith(
                  {
                    externalId: '123',
                    externalName: 'ThirdPartyID',
                    service: 'entrata',
                    objectId: spaceId.toString(),
                    communityId: communityId.toString(),
                    collectionPath: 'spaces'
                  },
                  {
                    externalId: '123',
                    externalName: 'ThirdPartyID',
                    service: 'entrata',
                    objectId: spaceId.toString(),
                    communityId: communityId.toString(),
                    collectionPath: 'spaces'
                  }
                )
                expect(spyMarScanRequestAsUploaded).toHaveBeenCalledTimes(0)
              })
            })
          })

          describe('And space is not isComplete', () => {
            describe('And scheduledFor is passed', () => {
              it('should not call markScanRequestAsUploaded', async () => {
                const partialSpace = { isComplete: false }
                const scheduledFor = new Date()
                jest
                  .spyOn(spaceRepositoryMongoDb, 'findById')
                  .mockResolvedValueOnce({
                    ...partialSpace,
                    _id: space._id
                  } as Space)

                jest
                  .spyOn(spaceRepositoryMongoDb, 'update')
                  .mockResolvedValueOnce(undefined)

                const spyMarScanRequestAsUploaded = jest
                  .spyOn(scanRequestService, 'markScanRequestAsUploaded')
                  .mockResolvedValueOnce({} as any)

                const externalLinks = [
                  {
                    externalId: '123',
                    externalName: ExternalLinkName.ThirdPartyId,
                    service: ExternalLinkService.Entrata
                  }
                ]

                await spaceService.update(
                  spaceId.toString(),
                  partialSpace,
                  null,
                  false,
                  externalLinks,
                  null,
                  scheduledFor
                )

                const expectedOptions = null
                expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                  spaceId.toString(),
                  flattenObjectKeys(partialSpace),
                  expectedOptions
                )

                jest
                  .spyOn(spaceRepositoryMongoDb, 'create')
                  .mockResolvedValueOnce(space)

                expect(
                  externalLinkRepository.addOrUpdateExternalLinkBy
                ).toHaveReturnedTimes(1)
                expect(
                  externalLinkRepository.addOrUpdateExternalLinkBy
                ).toHaveBeenCalledWith(
                  {
                    externalId: '123',
                    externalName: 'ThirdPartyID',
                    service: 'entrata',
                    objectId: spaceId.toString(),
                    communityId: communityId.toString(),
                    collectionPath: 'spaces'
                  },
                  {
                    externalId: '123',
                    externalName: 'ThirdPartyID',
                    service: 'entrata',
                    objectId: spaceId.toString(),
                    communityId: communityId.toString(),
                    collectionPath: 'spaces'
                  }
                )
                expect(spyMarScanRequestAsUploaded).toHaveBeenCalledTimes(0)
              })
            })

            describe('And scheduledFor is not passed', () => {
              it('should not call markScanRequestAsUploaded', async () => {
                const partialSpace = { isComplete: false }
                const scheduledFor = undefined
                jest
                  .spyOn(spaceRepositoryMongoDb, 'findById')
                  .mockResolvedValueOnce({
                    ...partialSpace,
                    _id: space._id
                  } as Space)

                jest
                  .spyOn(spaceRepositoryMongoDb, 'update')
                  .mockResolvedValueOnce(undefined)

                const spyMarScanRequestAsUploaded = jest
                  .spyOn(scanRequestService, 'markScanRequestAsUploaded')
                  .mockResolvedValueOnce({} as any)

                const externalLinks = [
                  {
                    externalId: '123',
                    externalName: ExternalLinkName.ThirdPartyId,
                    service: ExternalLinkService.Entrata
                  }
                ]

                await spaceService.update(
                  spaceId.toString(),
                  partialSpace,
                  null,
                  false,
                  externalLinks,
                  null,
                  scheduledFor
                )

                const expectedOptions = null
                expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                  spaceId.toString(),
                  flattenObjectKeys(partialSpace),
                  expectedOptions
                )

                jest
                  .spyOn(spaceRepositoryMongoDb, 'create')
                  .mockResolvedValueOnce(space)

                expect(
                  externalLinkRepository.addOrUpdateExternalLinkBy
                ).toHaveReturnedTimes(1)
                expect(
                  externalLinkRepository.addOrUpdateExternalLinkBy
                ).toHaveBeenCalledWith(
                  {
                    externalId: '123',
                    externalName: 'ThirdPartyID',
                    service: 'entrata',
                    objectId: spaceId.toString(),
                    communityId: communityId.toString(),
                    collectionPath: 'spaces'
                  },
                  {
                    externalId: '123',
                    externalName: 'ThirdPartyID',
                    service: 'entrata',
                    objectId: spaceId.toString(),
                    communityId: communityId.toString(),
                    collectionPath: 'spaces'
                  }
                )
                expect(spyMarScanRequestAsUploaded).toHaveBeenCalledTimes(0)
              })
            })
          })

          describe('AND space has tourLastUpdatedDate', () => {
            it('should update space and update scan requests', async () => {
              const partialSpace = {
                bedrooms: 2,
                bathrooms: 1,
                rentPrices: [
                  {
                    termInMonths: 12,
                    price: 1000,
                    enabled: true
                  }
                ],
                tourLastUpdatedDate: new Date()
              }
              const scanRequestId1 = generateId()
              const scanRequestId2 = generateId()
              const scanRequests = [
                {
                  _id: scanRequestId1,
                  space: {
                    _id: spaceId
                  }
                },
                {
                  _id: scanRequestId2,
                  space: {
                    _id: spaceId
                  }
                }
              ]

              jest
                .spyOn(spaceRepositoryMongoDb, 'findById')
                .mockResolvedValueOnce({
                  ...partialSpace,
                  _id: space._id
                } as Space)

              jest
                .spyOn(scanRequestRepository, 'findScanRequestsByQuery')
                .mockResolvedValueOnce(scanRequests as ScanRequest[])

              const updateScanRequestSpy = jest
                .spyOn(scanRequestRepository, 'updateScanRequests')
                .mockResolvedValueOnce(undefined)

              jest
                .spyOn(spaceRepositoryMongoDb, 'update')
                .mockResolvedValueOnce({
                  ...partialSpace,
                  _id: space._id
                } as Space)

              await spaceService.update(spaceId.toString(), partialSpace)

              const expectedOptions = undefined
              expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                spaceId.toString(),
                flattenObjectKeys(partialSpace),
                expectedOptions
              )
              expect(partialSpace['displayPrice']).toEqual(1000)
              expect(updateScanRequestSpy).toHaveBeenCalledWith(
                [scanRequestId1.toString(), scanRequestId2.toString()],
                {
                  'space.tourLastUpdatedDate': partialSpace.tourLastUpdatedDate
                }
              )
            })

            describe('AND has no scan request for the space', () => {
              it('should update space and not update scan requests', async () => {
                const partialSpace = {
                  bedrooms: 2,
                  bathrooms: 1,
                  rentPrices: [
                    {
                      termInMonths: 12,
                      price: 1000,
                      enabled: true
                    }
                  ],
                  tourLastUpdatedDate: new Date()
                }

                jest
                  .spyOn(spaceRepositoryMongoDb, 'findById')
                  .mockResolvedValueOnce({
                    ...partialSpace,
                    _id: space._id
                  } as Space)

                jest
                  .spyOn(scanRequestRepository, 'findScanRequestsByQuery')
                  .mockResolvedValueOnce([])

                const updateScanRequestSpy = jest
                  .spyOn(scanRequestRepository, 'updateScanRequests')
                  .mockResolvedValueOnce(undefined)

                jest
                  .spyOn(spaceRepositoryMongoDb, 'update')
                  .mockResolvedValueOnce({
                    ...partialSpace,
                    _id: space._id
                  } as Space)

                await spaceService.update(spaceId.toString(), partialSpace)

                const expectedOptions = undefined
                expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                  spaceId.toString(),
                  flattenObjectKeys(partialSpace),
                  expectedOptions
                )
                expect(partialSpace['displayPrice']).toEqual(1000)
                expect(updateScanRequestSpy).not.toHaveBeenCalled()
              })
            })

            describe('AND has an error to update scan request', () => {
              it('should update space and not update scan requests', async () => {
                const partialSpace = {
                  bedrooms: 2,
                  bathrooms: 1,
                  rentPrices: [
                    {
                      termInMonths: 12,
                      price: 1000,
                      enabled: true
                    }
                  ],
                  tourLastUpdatedDate: new Date()
                }
                const scanRequestId1 = generateId()
                const scanRequestId2 = generateId()
                const scanRequests = [
                  {
                    _id: scanRequestId1,
                    space: {
                      _id: spaceId
                    }
                  },
                  {
                    _id: scanRequestId2,
                    space: {
                      _id: spaceId
                    }
                  }
                ]

                jest
                  .spyOn(spaceRepositoryMongoDb, 'findById')
                  .mockResolvedValueOnce({
                    ...partialSpace,
                    _id: space._id
                  } as Space)

                jest
                  .spyOn(scanRequestRepository, 'findScanRequestsByQuery')
                  .mockResolvedValueOnce(scanRequests as ScanRequest[])

                const updateScanRequestSpy = jest
                  .spyOn(scanRequestRepository, 'updateScanRequests')
                  .mockRejectedValueOnce(new Error('Error'))

                const expected = {
                  ...partialSpace,
                  _id: space._id
                } as Space

                jest
                  .spyOn(spaceRepositoryMongoDb, 'update')
                  .mockResolvedValueOnce(expected)

                const logSpy = jest
                  .spyOn(logger, 'logWarn')
                  .mockReturnValueOnce(undefined)

                const result = await spaceService.update(
                  spaceId.toString(),
                  partialSpace
                )

                const expectedOptions = undefined
                expect(spaceRepositoryMongoDb.update).toHaveBeenCalledWith(
                  spaceId.toString(),
                  flattenObjectKeys(partialSpace),
                  expectedOptions
                )
                expect(partialSpace['displayPrice']).toEqual(1000)
                expect(updateScanRequestSpy).toHaveBeenCalledWith(
                  [scanRequestId1.toString(), scanRequestId2.toString()],
                  {
                    'space.tourLastUpdatedDate':
                      partialSpace.tourLastUpdatedDate
                  }
                )
                expect(result).toEqual(expected)
                expect(logSpy).toHaveBeenCalledWith(
                  expect.any(String),
                  'Error updating scan requests',
                  { error: 'Error' }
                )
              })
            })
          })
        })

        describe('AND community does not exist', () => {
          beforeEach(() => {
            jest.resetAllMocks()
          })

          it('should raise an error', async () => {
            jest
              .spyOn(spaceRepositoryMongoDb, 'findById')
              .mockResolvedValueOnce(space)

            await expect(
              spaceService.update(spaceId.toString(), space)
            ).rejects.toThrowError()
          })
        })
      })
    })
  })

  describe('#findSpaceByCoordinates', () => {
    const spaceId = generateId()
    describe('WHEN space not found', () => {
      it('should return an empty array', async () => {
        jest
          .spyOn(spaceRepositoryMongoDb, 'findById')
          .mockResolvedValue({} as Space)

        const result = await spaceService.findSpaceByCoordinates(
          spaceId.toString()
        )

        expect(result).toEqual([])
      })
    })

    describe('WHEN space is found', () => {
      describe('AND building location is not found', () => {
        it('should return an empty array', async () => {
          jest
            .spyOn(spaceRepositoryMongoDb, 'findById')
            .mockResolvedValueOnce({ _id: spaceId } as Space)

          const result = await spaceService.findSpaceByCoordinates(
            spaceId.toString()
          )
          expect(result).toEqual([])
        })
      })

      describe('AND community is not found', () => {
        it('should return an empty array', async () => {
          jest
            .spyOn(spaceRepositoryMongoDb, 'findById')
            .mockResolvedValueOnce({ _id: spaceId } as Space)

          jest
            .spyOn(communityService, 'findCommunityById')
            .mockResolvedValueOnce(undefined)

          const result = await spaceService.findSpaceByCoordinates(
            spaceId.toString()
          )

          expect(result).toEqual([])
        })
      })

      describe('AND community is found', () => {
        describe('AND regionalLeasingSettings is not enabled', () => {
          it('should return spaces with the same community, with distanceFromSimilar 0', async () => {
            const foundSpaceSameCommunity = {
              _id: generateId(),
              isVisible: true,
              type: SpaceType.Unit,
              deletedAt: null,
              bedrooms: 1,
              bathrooms: 1,
              token: '123',
              building: {
                location: {
                  coordinates: [1, 1]
                }
              },
              rentPrices: [
                {
                  termsInMonths: 12,
                  price: 1000,
                  enabled: true
                }
              ],
              community: {
                _id: communityId,
                name: 'test'
              },
              coverPhoto: {
                url: 'https://example.com/image.jpg'
              }
            }

            jest
              .spyOn(spaceRepositoryMongoDb, 'findAll')
              .mockResolvedValueOnce([foundSpaceSameCommunity] as any)
            jest
              .spyOn(communityService, 'findCommunityById')
              .mockResolvedValueOnce({
                _id: communityId,
                regionalLeasingSettings: { enabled: false }
              } as Community)
            jest
              .spyOn(spaceRepositoryMongoDb, 'findById')
              .mockResolvedValueOnce({
                _id: spaceId,
                isVisible: true,
                type: SpaceType.Unit,
                deletedAt: null,
                bedrooms: 1,
                building: {
                  location: {
                    coordinates: [1, 1]
                  }
                },
                rentPrices: [
                  {
                    termsInMonths: 12,
                    price: 1000,
                    enabled: true
                  }
                ],
                community: {
                  _id: communityId
                }
              } as any)

            const result = await spaceService.findSpaceByCoordinates(
              spaceId.toString()
            )

            expect(result).toEqual([])
          })
        })

        describe('WHEN space community has regionalLeasingSettings disabled', () => {
          it('should return an empty array', async () => {
            jest
              .spyOn(spaceRepositoryMongoDb, 'findById')
              .mockResolvedValueOnce({
                _id: spaceId,
                community: {
                  _id: communityId
                }
              } as any)

            jest
              .spyOn(communityService, 'findCommunityById')
              .mockResolvedValueOnce({
                _id: communityId,
                regionalLeasingSettings: {
                  enabled: false
                }
              } as Community)

            const result = await spaceService.findSpaceByCoordinates(
              spaceId.toString()
            )

            expect(result).toEqual([])
          })
        })

        describe('WHEN space community has no regionalLeasingSettings', () => {
          it('should return an empty array', async () => {
            jest
              .spyOn(spaceRepositoryMongoDb, 'findById')
              .mockResolvedValueOnce({
                _id: spaceId,
                community: {
                  _id: communityId
                }
              } as any)

            jest
              .spyOn(communityService, 'findCommunityById')
              .mockResolvedValueOnce({
                _id: communityId
              } as Community)

            const result = await spaceService.findSpaceByCoordinates(
              spaceId.toString()
            )

            expect(result).toEqual([])
          })
        })

        describe('AND regionalLeasingSettings is enabled', () => {
          const communityId2 = generateId()

          beforeEach(() => {
            jest.clearAllMocks()
            jest.resetAllMocks()
          })

          describe('AND spaces has rentPrices', () => {
            it('should return spaces with the community in communitiesGroup', async () => {
              const foundSpaceSameCommunity = {
                _id: generateId(),
                isVisible: true,
                type: SpaceType.Unit,
                deletedAt: null,
                bedrooms: 1,
                bathrooms: 1,
                token: '123',
                building: {
                  location: {
                    coordinates: [2000, 2000]
                  }
                },
                rentPrices: [
                  {
                    termsInMonths: 12,
                    price: 1000,
                    enabled: true
                  }
                ],
                community: {
                  _id: communityId2,
                  name: 'test'
                },
                coverPhoto: {
                  url: 'https://example.com/image.jpg'
                }
              }

              const spyFindAll = jest
                .spyOn(spaceRepositoryMongoDb, 'findAll')
                .mockResolvedValueOnce([foundSpaceSameCommunity] as any)

              jest
                .spyOn(communityService, 'findCommunityById')
                .mockResolvedValueOnce({
                  _id: communityId,
                  regionalLeasingSettings: {
                    enabled: true,
                    communitiesGroup: [communityId2.toString()]
                  }
                } as Community)

              jest
                .spyOn(spaceRepositoryMongoDb, 'findById')
                .mockResolvedValueOnce({
                  _id: spaceId,
                  isVisible: true,
                  type: SpaceType.Unit,
                  deletedAt: null,
                  bedrooms: 1,
                  bathrooms: 1,
                  token: '123',
                  building: {
                    location: {
                      coordinates: [1, 1]
                    }
                  },
                  community: {
                    _id: communityId
                  },
                  coverPhoto: {
                    url: 'https://example.com/image.jpg'
                  }
                } as any)

              const result = await spaceService.findSpaceByCoordinates(
                spaceId.toString()
              )

              const expectedResult = [
                {
                  _id: foundSpaceSameCommunity._id,
                  bedrooms: foundSpaceSameCommunity.bedrooms,
                  bathrooms: foundSpaceSameCommunity.bathrooms,
                  rentPrices: foundSpaceSameCommunity.rentPrices,
                  token: foundSpaceSameCommunity.token,
                  coverPhoto: {
                    url: foundSpaceSameCommunity.coverPhoto.url
                  },
                  type: foundSpaceSameCommunity.type,
                  community: {
                    _id: foundSpaceSameCommunity.community._id.toString(),
                    name: foundSpaceSameCommunity.community.name
                  },
                  distanceFromSimilar: 1939.3108276272483
                }
              ]

              const expectedQuery = {
                _id: {
                  $ne: spaceId
                },
                'community._id': {
                  $in: [communityId, communityId2]
                },
                isVisible: true,
                isComplete: true,
                type: 'unit',
                deletedAt: null,
                bedrooms: {
                  $gte: 1
                },
                'building.location': {
                  $near: {
                    $geometry: {
                      type: 'Point',
                      coordinates: [1, 1]
                    }
                  }
                }
              }

              expect(spyFindAll).toHaveBeenCalledWith(expectedQuery, {
                limit: 50,
                projection:
                  '_id bedrooms bathrooms rentPrices.price rentPrices.termInMonths token coverPhoto.url type community._id community.name community.organization._id building.location floorPlan.name displayPrice'
              })
              expect(result).toEqual(expectedResult)
            })

            describe('AND community has showPrices true', () => {
              it('should return spaces with prices fields', async () => {
                const foundSpaceSameCommunity = {
                  _id: generateId(),
                  isVisible: true,
                  type: SpaceType.Unit,
                  deletedAt: null,
                  bedrooms: 1,
                  bathrooms: 1,
                  token: '123',
                  building: {
                    location: {
                      coordinates: [2000, 2000]
                    }
                  },
                  rentPrices: [
                    {
                      termsInMonths: 12,
                      price: 1000,
                      enabled: true
                    }
                  ],
                  community: {
                    _id: communityId2,
                    name: 'test',
                    showPrices: true
                  },
                  coverPhoto: {
                    url: 'https://example.com/image.jpg'
                  },
                  displayPrice: 1000
                }

                const spyFindAll = jest
                  .spyOn(spaceRepositoryMongoDb, 'findAll')
                  .mockResolvedValueOnce([foundSpaceSameCommunity] as any)

                jest
                  .spyOn(communityService, 'findCommunityById')
                  .mockResolvedValueOnce({
                    _id: communityId,
                    regionalLeasingSettings: {
                      enabled: true,
                      communitiesGroup: [communityId2.toString()],
                      priceRange: 10
                    },
                    showPrices: true
                  } as Community)
                jest
                  .spyOn(communityService, 'findCommunityById')
                  .mockResolvedValueOnce({
                    _id: communityId,
                    showPrices: true
                  } as Community)

                jest
                  .spyOn(spaceRepositoryMongoDb, 'findById')
                  .mockResolvedValueOnce({
                    _id: spaceId,
                    isVisible: true,
                    type: SpaceType.Unit,
                    deletedAt: null,
                    bedrooms: 1,
                    bathrooms: 1,
                    token: '123',
                    building: {
                      location: {
                        coordinates: [1, 1]
                      }
                    },
                    community: {
                      _id: communityId
                    },
                    coverPhoto: {
                      url: 'https://example.com/image.jpg'
                    }
                  } as any)

                const result = await spaceService.findSpaceByCoordinates(
                  spaceId.toString()
                )

                const expectedResult = [
                  {
                    _id: foundSpaceSameCommunity._id,
                    bedrooms: foundSpaceSameCommunity.bedrooms,
                    bathrooms: foundSpaceSameCommunity.bathrooms,
                    rentPrices: foundSpaceSameCommunity.rentPrices,
                    token: foundSpaceSameCommunity.token,
                    coverPhoto: {
                      url: foundSpaceSameCommunity.coverPhoto.url
                    },
                    type: foundSpaceSameCommunity.type,
                    community: {
                      _id: foundSpaceSameCommunity.community._id.toString(),
                      name: foundSpaceSameCommunity.community.name
                    },
                    distanceFromSimilar: 1939.3108276272483,
                    displayPrice: 1000
                  }
                ]

                const expectedQuery = {
                  _id: {
                    $ne: spaceId
                  },
                  'community._id': {
                    $in: [communityId, communityId2]
                  },
                  isVisible: true,
                  isComplete: true,
                  type: 'unit',
                  deletedAt: null,
                  bedrooms: {
                    $gte: 1
                  },
                  'building.location': {
                    $near: {
                      $geometry: {
                        type: 'Point',
                        coordinates: [1, 1]
                      }
                    }
                  }
                }

                expect(spyFindAll).toHaveBeenCalledWith(expectedQuery, {
                  limit: 50,
                  projection:
                    '_id bedrooms bathrooms rentPrices.price rentPrices.termInMonths token coverPhoto.url type community._id community.name community.organization._id building.location floorPlan.name displayPrice'
                })
                expect(result).toEqual(expectedResult)
              })
            })

            describe('AND community has showPrices false', () => {
              it('should return spaces without prices fields', async () => {
                const foundSpaceSameCommunity = {
                  _id: generateId(),
                  isVisible: true,
                  type: SpaceType.Unit,
                  deletedAt: null,
                  bedrooms: 1,
                  bathrooms: 1,
                  token: '123',
                  building: {
                    location: {
                      coordinates: [2000, 2000]
                    }
                  },
                  rentPrices: [
                    {
                      termsInMonths: 12,
                      price: 1000,
                      enabled: true
                    }
                  ],
                  community: {
                    _id: communityId2,
                    name: 'test',
                    showPrices: false
                  },
                  coverPhoto: {
                    url: 'https://example.com/image.jpg'
                  },
                  displayPrice: 1000
                }

                const spyFindAll = jest
                  .spyOn(spaceRepositoryMongoDb, 'findAll')
                  .mockResolvedValueOnce([foundSpaceSameCommunity] as any)

                jest
                  .spyOn(communityService, 'findCommunityById')
                  .mockResolvedValueOnce({
                    _id: communityId,
                    regionalLeasingSettings: {
                      enabled: true,
                      communitiesGroup: [communityId2.toString()],
                      priceRange: 10
                    },
                    showPrices: false
                  } as Community)
                jest
                  .spyOn(communityService, 'findCommunityById')
                  .mockResolvedValueOnce({
                    _id: communityId,
                    showPrices: false
                  } as Community)

                jest
                  .spyOn(spaceRepositoryMongoDb, 'findById')
                  .mockResolvedValueOnce({
                    _id: spaceId,
                    isVisible: true,
                    type: SpaceType.Unit,
                    deletedAt: null,
                    bedrooms: 1,
                    bathrooms: 1,
                    displayPrice: 1000,
                    token: '123',
                    building: {
                      location: {
                        coordinates: [1, 1]
                      }
                    },
                    community: {
                      _id: communityId
                    },
                    coverPhoto: {
                      url: 'https://example.com/image.jpg'
                    }
                  } as any)

                const result = await spaceService.findSpaceByCoordinates(
                  spaceId.toString()
                )

                const expectedResult = [
                  {
                    _id: foundSpaceSameCommunity._id,
                    bedrooms: foundSpaceSameCommunity.bedrooms,
                    bathrooms: foundSpaceSameCommunity.bathrooms,
                    token: foundSpaceSameCommunity.token,
                    coverPhoto: {
                      url: foundSpaceSameCommunity.coverPhoto.url
                    },
                    type: foundSpaceSameCommunity.type,
                    community: {
                      _id: foundSpaceSameCommunity.community._id.toString(),
                      name: foundSpaceSameCommunity.community.name
                    },
                    distanceFromSimilar: 1939.3108276272483
                  }
                ]

                const expectedQuery = {
                  _id: {
                    $ne: spaceId
                  },
                  'community._id': {
                    $in: [communityId, communityId2]
                  },
                  isVisible: true,
                  isComplete: true,
                  type: 'unit',
                  deletedAt: null,
                  bedrooms: {
                    $gte: 1
                  },
                  displayPrice: {
                    $gte: 900,
                    $lte: 1100
                  },
                  'building.location': {
                    $near: {
                      $geometry: {
                        type: 'Point',
                        coordinates: [1, 1]
                      }
                    }
                  }
                }

                expect(spyFindAll).toHaveBeenCalledWith(expectedQuery, {
                  limit: 50,
                  projection:
                    '_id bedrooms bathrooms rentPrices.price rentPrices.termInMonths token coverPhoto.url type community._id community.name community.organization._id building.location floorPlan.name displayPrice'
                })

                expect(result).toEqual(expectedResult)
              })
            })

            describe('AND community has showPrices false', () => {
              describe('AND has showPrices true', () => {
                it('should return spaces without prices fields', async () => {
                  const foundSpaceSameCommunity = {
                    _id: generateId(),
                    isVisible: true,
                    type: SpaceType.Unit,
                    deletedAt: null,
                    bedrooms: 1,
                    bathrooms: 1,
                    token: '123',
                    building: {
                      location: {
                        coordinates: [2000, 2000]
                      }
                    },
                    rentPrices: [
                      {
                        termsInMonths: 12,
                        price: 1000,
                        enabled: true
                      }
                    ],
                    community: {
                      _id: communityId2,
                      name: 'test',
                      showPrices: false
                    },
                    coverPhoto: {
                      url: 'https://example.com/image.jpg'
                    },
                    displayPrice: 1000
                  }

                  const otherSpace = {
                    _id: generateId(),
                    isVisible: true,
                    type: SpaceType.Unit,
                    deletedAt: null,
                    bedrooms: 1,
                    bathrooms: 1,
                    token: '123',
                    building: {
                      location: {
                        coordinates: [2000, 2000]
                      }
                    },
                    rentPrices: [
                      {
                        termsInMonths: 12,
                        price: 1000,
                        enabled: true
                      }
                    ],
                    community: {
                      _id: communityId,
                      name: 'test',
                      showPrices: false
                    },
                    coverPhoto: {
                      url: 'https://example.com/image.jpg'
                    },
                    displayPrice: 1000
                  }

                  const spyFindAll = jest
                    .spyOn(spaceRepositoryMongoDb, 'findAll')
                    .mockResolvedValueOnce([
                      foundSpaceSameCommunity,
                      otherSpace
                    ] as any)

                  jest
                    .spyOn(communityService, 'findCommunityById')
                    .mockResolvedValueOnce({
                      _id: communityId,
                      regionalLeasingSettings: {
                        enabled: true,
                        communitiesGroup: [communityId2.toString()],
                        priceRange: 10
                      },
                      showPrices: true
                    } as Community)
                  jest
                    .spyOn(communityService, 'findCommunityById')
                    .mockResolvedValueOnce({
                      _id: communityId2,
                      showPrices: false
                    } as Community)

                  jest
                    .spyOn(spaceRepositoryMongoDb, 'findById')
                    .mockResolvedValueOnce({
                      _id: spaceId,
                      isVisible: true,
                      type: SpaceType.Unit,
                      deletedAt: null,
                      bedrooms: 1,
                      bathrooms: 1,
                      displayPrice: 1000,
                      token: '123',
                      building: {
                        location: {
                          coordinates: [1, 1]
                        }
                      },
                      community: {
                        _id: communityId
                      },
                      coverPhoto: {
                        url: 'https://example.com/image.jpg'
                      }
                    } as any)

                  const result = await spaceService.findSpaceByCoordinates(
                    spaceId.toString()
                  )

                  const expectedResult = [
                    {
                      _id: otherSpace._id,
                      bedrooms: otherSpace.bedrooms,
                      bathrooms: otherSpace.bathrooms,
                      token: otherSpace.token,
                      coverPhoto: {
                        url: otherSpace.coverPhoto.url
                      },
                      type: otherSpace.type,
                      community: {
                        _id: otherSpace.community._id.toString(),
                        name: otherSpace.community.name
                      },
                      distanceFromSimilar: 0,
                      displayPrice: otherSpace.displayPrice,
                      rentPrices: otherSpace.rentPrices
                    },
                    {
                      _id: foundSpaceSameCommunity._id,
                      bedrooms: foundSpaceSameCommunity.bedrooms,
                      bathrooms: foundSpaceSameCommunity.bathrooms,
                      token: foundSpaceSameCommunity.token,
                      coverPhoto: {
                        url: foundSpaceSameCommunity.coverPhoto.url
                      },
                      type: foundSpaceSameCommunity.type,
                      community: {
                        _id: foundSpaceSameCommunity.community._id.toString(),
                        name: foundSpaceSameCommunity.community.name
                      },
                      distanceFromSimilar: 1939.3108276272483
                    }
                  ]

                  const expectedQuery = {
                    _id: {
                      $ne: spaceId
                    },
                    'community._id': {
                      $in: [communityId, communityId2]
                    },
                    isVisible: true,
                    isComplete: true,
                    type: 'unit',
                    deletedAt: null,
                    bedrooms: {
                      $gte: 1
                    },
                    displayPrice: {
                      $gte: 900,
                      $lte: 1100
                    },
                    'building.location': {
                      $near: {
                        $geometry: {
                          type: 'Point',
                          coordinates: [1, 1]
                        }
                      }
                    }
                  }

                  expect(spyFindAll).toHaveBeenCalledWith(expectedQuery, {
                    limit: 50,
                    projection:
                      '_id bedrooms bathrooms rentPrices.price rentPrices.termInMonths token coverPhoto.url type community._id community.name community.organization._id building.location floorPlan.name displayPrice'
                  })

                  expect(result).toEqual(expectedResult)
                })
              })
            })
          })

          describe('AND spaces has no rentPrices', () => {
            it('should match by floorplan name', async () => {
              const foundSpaceSameCommunity = {
                _id: generateId(),
                isVisible: true,
                type: SpaceType.Unit,
                deletedAt: null,
                floorPlan: {
                  name: 'test'
                },
                community: {
                  _id: communityId,
                  name: 'test'
                },
                coverPhoto: {
                  url: 'https://example.com/image.jpg'
                },
                building: {
                  location: {
                    coordinates: [1, 1]
                  }
                },
                rentPrices: []
              }

              const spyFindAll = jest
                .spyOn(spaceRepositoryMongoDb, 'findAll')
                .mockResolvedValueOnce([foundSpaceSameCommunity] as any)

              jest
                .spyOn(communityService, 'findCommunityById')
                .mockResolvedValueOnce({
                  _id: communityId,
                  regionalLeasingSettings: {
                    enabled: true,
                    communitiesGroup: [communityId2.toString()]
                  }
                } as Community)

              jest
                .spyOn(spaceRepositoryMongoDb, 'findById')
                .mockResolvedValueOnce({
                  _id: spaceId,
                  isVisible: true,
                  type: SpaceType.Unit,
                  deletedAt: null,
                  bedrooms: 1,
                  bathrooms: 1,
                  token: '123',
                  building: {
                    location: {
                      coordinates: [1, 1]
                    }
                  },
                  community: {
                    _id: communityId
                  },
                  coverPhoto: {
                    url: 'https://example.com/image.jpg'
                  },
                  floorPlan: {
                    name: 'test'
                  },
                  rentPrices: undefined
                } as any)

              const result = await spaceService.findSpaceByCoordinates(
                spaceId.toString()
              )

              const expectedQuery = {
                _id: {
                  $ne: spaceId
                },
                'community._id': {
                  $in: [communityId, communityId2]
                },
                isVisible: true,
                isComplete: true,
                type: SpaceType.Unit,
                deletedAt: null,
                bedrooms: {
                  $gte: 1
                },
                'building.location': {
                  $near: {
                    $geometry: {
                      type: 'Point',
                      coordinates: [1, 1]
                    }
                  }
                },
                'floorPlan.name': 'test'
              }

              expect(result).toEqual([
                {
                  _id: foundSpaceSameCommunity._id,
                  rentPrices: [],
                  coverPhoto: {
                    url: foundSpaceSameCommunity.coverPhoto.url
                  },
                  type: 'unit',
                  community: {
                    _id: foundSpaceSameCommunity.community._id.toString(),
                    name: foundSpaceSameCommunity.community.name
                  },
                  distanceFromSimilar: 0
                }
              ])
              expect(spyFindAll).toHaveBeenCalledWith(expectedQuery, {
                limit: 50,
                projection:
                  '_id bedrooms bathrooms rentPrices.price rentPrices.termInMonths token coverPhoto.url type community._id community.name community.organization._id building.location floorPlan.name displayPrice'
              })
            })
          })
        })
      })
    })
  })
})
