import {
  removeUndef,
  convertStringToBoolean,
  isNumeric,
  hotspotToPosition
} from '@core/util'

describe('util', () => {
  describe('#removeUndef', () => {
    it('should not modify the original object', () => {
      const obj = { a: 'one', b: 2, c: [3, 4] }
      const result = removeUndef(obj)
      expect(result).not.toBe(obj)
    })

    it('should not remove defined', () => {
      const obj = { a: 'one', b: 2, c: [3, 4] }
      const keys = Object.keys(removeUndef(obj))
      expect(keys).toEqual(Object.keys(obj))
    })

    it('should not remove false like values', () => {
      const keys = Object.keys(
        removeUndef({ a: undefined, b: null, c: 0, d: false })
      )
      expect(keys).toEqual(['b', 'c', 'd'])
    })

    it('should recurse into nested objects', () => {
      const obj = {
        a: 'one',
        b: 2,
        c: [3, 4],
        d: undefined,
        sub: { z: undefined, y: 'not' }
      }
      const result = removeUndef(obj)
      expect(Object.keys(result)).toEqual(['a', 'b', 'c', 'sub'])
      expect(Object.keys(result.sub)).toEqual(['y'])
    })
  })

  describe('#convertStringToBoolean', () => {
    describe('when receive a string "true"', () => {
      it('should convert to boolean true', () => {
        expect(convertStringToBoolean('true')).toBe(true)
      })
    })

    describe('when receive string "false"', () => {
      it('should convert to boolean false', () => {
        expect(convertStringToBoolean('false')).toBe(false)
      })
    })
  })

  describe('#isNumeric', () => {
    it('"123" is numeric', () => {
      const result = isNumeric('123')
      expect(result).toEqual(true)
    })

    it('"123.4" is numeric', () => {
      const result = isNumeric('123.4')
      expect(result).toEqual(true)
    })

    it('"12 3.4" is not numeric', () => {
      const result = isNumeric('12 3.4')
      expect(result).toEqual(false)
    })

    it('empty string is not numeric', () => {
      const result1 = isNumeric('')
      expect(result1).toEqual(false)
      const result2 = isNumeric(' ')
      expect(result2).toEqual(false)
    })

    it('"a" is not numeric', () => {
      const result = isNumeric('a')
      expect(result).toEqual(false)
    })

    it('"1#" is not numeric', () => {
      const result = isNumeric('1#')
      expect(result).toEqual(false)
    })
  })

  describe('#hotspotToPosition', () => {
    // Data taken from spaces that were observed to be correct
    it.only.each(
      [
        {
          input: { pitch: 0, yaw: -5.2479353 },
          output: { x: -477.9879476463937, z: 43.90355230261388, y: 0 }
        },
        {
          input: { pitch: 0, yaw: 158.1604 },
          output: { x: 445.54988837549973, z: -178.56454566508913, y: 0 }
        },
        {
          input: { pitch: 0, yaw: 162.6382 },
          output: { x: 458.13095598250135, z: -143.2341690050227, y: 0 }
        }
      ].map(({ input, output }) => [
        `pitch: ${input.pitch}, yaw: ${input.yaw}`,
        input,
        output
      ])
    )(
      'it should calculate correctly the position based on pitch and yaw for %s',
      (_, input, output) => {
        const position = hotspotToPosition(input.pitch, input.yaw)
        expect(position).toEqual(output)
      }
    )
  })
})
