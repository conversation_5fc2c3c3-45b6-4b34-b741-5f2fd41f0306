import { app } from '@api/app'
import supertest from 'supertest'

import {
  clearAll,
  getToken,
  insertCommunities,
  insertPersonas,
  insertSpaces
} from './config/utils'

import { SpaceModel } from '@modules/communities/models/space'
import { insertOrganizations } from './config/utils'
import { OrganizationA } from './fixtures/organizations'
import { NodeA11, NodeB11, SpaceA11, SpaceB11 } from './fixtures/spaces'
import {
  allNonOrgAdminUsers,
  allNonSuperAdminUsers,
  allOrgAdminUsers,
  UserSuperAdminA
} from './fixtures/users'
import { Space } from '@modules/communities/types/space'

describe('Spaces', () => {
  beforeEach((done) => {
    Promise.allSettled([
      insertOrganizations(),
      insertPersonas(),
      insertCommunities(),
      insertSpaces()
    ]).then(() => {
      done()
    })
  })

  afterEach((done) => {
    clearAll().then(() => {
      done()
    })
  })

  describe('Update node', () => {
    it('Anonymous users should not be able to update node', async () => {
      const res = await supertest(app).put(
        `/spaces/${SpaceA11._id}/nodes/${NodeA11}`
      )
      expect(res.status).toBe(401)
    })
    it('Super admins should be able to update node', async () => {
      const token = await getToken(UserSuperAdminA)
      const res = await supertest(app)
        .put(`/spaces/${SpaceA11._id}/nodes/${NodeA11}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          label: 'New Kitchen'
        })
      expect(res.status).toBe(200)

      expect(res.body.nodes.length).toBe(1)
      expect(res.body.nodes[0]._id).toBe(NodeA11.toString())
      expect(res.body.nodes[0].label).toBe('New Kitchen')
    })

    it.each(allOrgAdminUsers.map((u) => [u.name, u]))(
      '%s should be able to update a node in its own organization',
      async (_, user) => {
        const space =
          user.organizations[0]._id === OrganizationA._id ? SpaceA11 : SpaceB11
        const node =
          user.organizations[0]._id === OrganizationA._id ? NodeA11 : NodeB11

        const token = await getToken(user)
        const res = await supertest(app)
          .put(`/spaces/${space._id}/nodes/${node}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            label: 'New Kitchen'
          })

        expect(res.status).toBe(200)
        expect(res.body.nodes[0]._id).toBe(node.toString())
        expect(res.body.nodes[0].label).toBe('New Kitchen')
      }
    )

    it.each(allNonOrgAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to update a node in its own organization',
      async (_, user) => {
        const space =
          user.organizations[0]._id === OrganizationA._id ? SpaceA11 : SpaceB11
        const node =
          user.organizations[0]._id === OrganizationA._id ? NodeA11 : NodeB11

        const token = await getToken(user)
        const res = await supertest(app)
          .put(`/spaces/${space._id}/nodes/${node}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            photo: {
              url: 'https://a-very-very-weird-image-possibly-illegal.jpg'
            }
          })

        expect(res.status).toBe(403)
      }
    )
    it.each(allNonSuperAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to update a node in a different organization',
      async (_, user) => {
        const space =
          user.organizations[0]._id === OrganizationA._id ? SpaceB11 : SpaceA11
        const node =
          user.organizations[0]._id === OrganizationA._id ? NodeB11 : NodeA11

        const token = await getToken(user)
        const res = await supertest(app)
          .put(`/spaces/${space._id}/nodes/${node}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            photo: {
              url: 'https://a-very-very-weird-image-possibly-illegal.jpg'
            }
          })

        expect(res.status).toBe(403)
      }
    )
  })
  describe('Update space', () => {
    it('Anonymous users should not be able to update space', async () => {
      const res = await supertest(app).put(`/spaces/${SpaceA11._id}`)
      expect(res.status).toBe(401)
    })
    it('Super admins should be able to update space', async () => {
      const token = await getToken(UserSuperAdminA)
      const res = await supertest(app)
        .put(`/spaces/${SpaceA11._id}`)
        .set('Authorization', `Bearer ${token}`)
        .send({
          isVisible: false
        })
      expect(res.status).toBe(200)

      expect(res.body.isVisible).toBe(false)
    })

    it.each(allOrgAdminUsers.map((u) => [u.name, u]))(
      '%s should be able to update a space in its own organization',
      async (_, user) => {
        const space =
          user.organizations[0]._id === OrganizationA._id ? SpaceA11 : SpaceB11

        const token = await getToken(user)
        const res = await supertest(app)
          .put(`/spaces/${space._id}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            isVisible: false
          })

        expect(res.status).toBe(200)
        expect(res.body.isVisible).toBe(false)
      }
    )

    it.each(allNonSuperAdminUsers.map((u) => [u.name, u]))(
      '%s should not be able to update a space in a different organization',
      async (_, user) => {
        const space =
          user.organizations[0]._id === OrganizationA._id ? SpaceB11 : SpaceA11

        const token = await getToken(user)
        const res = await supertest(app)
          .put(`/spaces/${space._id}`)
          .set('Authorization', `Bearer ${token}`)
          .send({
            isVisible: false
          })

        expect(res.status).toBe(403)
      }
    )

    describe('Node link position calculation', () => {
      describe('when processImage is true', () => {
        it('should calculate the position of the node links', async () => {
          const token = await getToken(UserSuperAdminA)
          const res = await supertest(app)
            .put(`/spaces/${SpaceA11._id}?processImage=true`)
            .set('Authorization', `Bearer ${token}`)
            .send({
              nodes: [
                {
                  label: 'Kitchen',
                  photo: {
                    url: `${SpaceA11._id.toString()}/KITCHEN.jpg`
                  },
                  nodeLinks: [
                    {
                      rotation: { pitch: 0, yaw: -5.2479353 }
                    },
                    {
                      rotation: { pitch: 0, yaw: 158.1604 }
                    },
                    {
                      rotation: { pitch: 0, yaw: 162.6382 }
                    }
                  ]
                }
              ]
            })

          expect(res.status).toBe(200)

          // Taken from spaces that were observed to be correct
          function expectPosition(space: Space) {
            expect(space.nodes[0].nodeLinks[0].position).toEqual({
              x: -477.9879476463937,
              z: 43.90355230261388,
              y: 0
            })
            expect(space.nodes[0].nodeLinks[1].position).toEqual({
              x: 445.54988837549973,
              z: -178.56454566508913,
              y: 0
            })
            expect(space.nodes[0].nodeLinks[2].position).toEqual({
              x: 458.13095598250135,
              z: -143.2341690050227,
              y: 0
            })
          }

          expectPosition(res.body)

          const savedSpace = await SpaceModel.findById(SpaceA11._id)
          expectPosition(savedSpace)
        })
      })
      describe('when processImage is not set', () => {
        it('should not calculate the position of the node links', async () => {
          const token = await getToken(UserSuperAdminA)
          const res = await supertest(app)
            .put(`/spaces/${SpaceA11._id}`)
            .set('Authorization', `Bearer ${token}`)
            .send({
              nodes: [
                {
                  label: 'Kitchen',
                  photo: {
                    url: `${SpaceA11._id.toString()}/KITCHEN.jpg`
                  },
                  nodeLinks: [
                    {
                      rotation: { pitch: 0, yaw: -5.2479353 }
                    },
                    {
                      rotation: { pitch: 0, yaw: 158.1604 }
                    },
                    {
                      rotation: { pitch: 0, yaw: 162.6382 }
                    }
                  ]
                }
              ]
            })

          expect(res.status).toBe(200)

          expect(res.body.nodes[0].nodeLinks[0].position).not.toBeDefined()
          expect(res.body.nodes[0].nodeLinks[1].position).not.toBeDefined()
          expect(res.body.nodes[0].nodeLinks[2].position).not.toBeDefined()
        })
      })
    })
  })
})
