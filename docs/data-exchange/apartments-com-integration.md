# Apartments.com Integration Documentation

## Overview
Apartments.com is a **data export integration** (not a traditional API integration) that generates and uploads property listing files to Apartments.com via FTP. Unlike other integrations that sync data bidirectionally, this integration is **outbound-only**, pushing Peek property data to Apartments.com for lead generation purposes.

## Key Architecture Features

### File-Based Data Export
- **Protocol**: FTP file upload (not REST/SOAP API)
- **Format**: JSON files with structured property data
- **Schedule**: Daily file generation with date-based naming
- **Direction**: **Outbound only** (Peek → Apartments.com)

### ILS (Internet Listing Service) Integration
- **Purpose**: Property listing syndication for lead generation
- **Configuration**: Uses `dataExchange.syncIls: true` (not `syncPms`)
- **Data Source**: Aggregates data from multiple PMS integrations
- **Output**: Standardized format for Apartments.com consumption

## Configuration (DX Settings)

⚠️ **Important**: Apartments.com integration has **no specific configuration fields** in DX settings. It only requires:

```typescript
// No ApartmentsDotComSetting interface - uses base DXSetting only
{
  service: DXSettingServices.APARTMENTS_DOT_COM,
  dataExchange: {
    syncIls: true  // ← Key flag for ILS sync
  }
}
```

### Environment Configuration
```typescript
// FTP Connection Settings
APARTMENT_FTP_HOST: string          // FTP server hostname
APARTMENT_FTP_PORT: string          // FTP server port
APARTMENT_FTP_USER: string          // FTP username
APARTMENT_FTP_PASSWORD: string      // FTP password
APARTMENT_DOT_COM_FILE_NAME: string // Base filename for uploads
APARTMENT_DOT_COM_BUCKET: string    // S3 bucket for file staging
```

## Data Export Process

### 1. File Generation (`generateFile.ts`)
- **Trigger**: Scheduled daily execution
- **Process**:
  1. Finds all DX settings with `syncIls: true`
  2. Iterates through each community's spaces
  3. Generates three types of data: Units, Amenities, Floor Plans
  4. Uploads consolidated JSON file to S3

### 2. FTP Upload (`uploadToFtp.ts`)
- **Trigger**: S3 file creation event
- **Process**:
  1. Downloads file from S3
  2. Uploads to Apartments.com FTP server
  3. Uses date-based filename (e.g., `20250813.json`)

## Data Structure

### Unit Data Export
```typescript
type ApartmentsDotComUnit = {
  FeedVendor: string                 // Source PMS (e.g., "Entrata", "RealPage")
  PropertyExternalReference: string  // Community external ID
  PropertyExternalName: string       // Community name
  ModelExternalReference: string     // Floor plan external ID
  ModelExternalName: string          // Floor plan name
  UnitExternalReference: string      // Unit external ID
  UnitExternalName: string           // Unit number/name
  URL: string                        // Peek web viewer URL
  ThumbnailImage: string             // Unit/floor plan image URL
  Caption: string                    // Unit description
}
```

### Floor Plan Data Export
```typescript
type ApartmentsDotComFloorPlan = {
  FeedVendor: string                 // Source PMS
  PropertyExternalReference: string  // Community external ID
  PropertyExternalName: string       // Community name
  ModelExternalReference: string     // Floor plan external ID
  ModelExternalName: string          // Floor plan name
  URL: string                        // Peek web viewer URL
  ThumbnailImage: string             // Floor plan image URL
  Caption: string                    // Floor plan description
}
```

### Amenity Data Export
```typescript
type ApartmentsDotComAmenity = {
  FeedVendor: string                 // Source PMS
  PropertyExternalReference: string  // Community external ID
  PropertyExternalName: string       // Community name
  URL: string                        // Amenity web viewer URL
  ThumbnailImage: string             // Amenity image URL
  Caption: string                    // Amenity description
}
```

## Serverless Functions

### `generateFile.ts` (Scheduled)
- **Purpose**: Daily generation of Apartments.com data export file
- **Schedule**: Runs daily (typically overnight)
- **Process**:
  1. Queries all communities with `syncIls: true`
  2. Processes spaces, floor plans, and amenities
  3. Generates consolidated JSON file
  4. Uploads to S3 staging bucket

### `uploadToFtp.ts` (S3 Event)
- **Purpose**: Uploads generated files to Apartments.com FTP server
- **Trigger**: S3 object creation in staging bucket
- **Process**:
  1. Downloads file from S3
  2. Connects to Apartments.com FTP server
  3. Uploads file with proper naming convention

## Data Aggregation Strategy

### Multi-PMS Data Source
⚠️ **Critical**: Apartments.com integration **aggregates data from multiple PMS systems**:

```typescript
// Excludes Apartments.com DX settings, includes all others
const communityDxSettings = await findAllDxSettings({
  communityId,
  service: { $ne: DXSettingServices.APARTMENTS_DOT_COM }, // ← Excludes self
  'dataExchange.syncPms': true  // ← Includes all PMS integrations
})
```

### Feed Vendor Mapping
- **Source Identification**: Each record includes `FeedVendor` field
- **PMS Attribution**: Maps to source PMS (Entrata, RealPage, Yardi, etc.)
- **Service Normalization**: RentCafe V2 mapped to RentCafe for consistency

### External ID Resolution
```typescript
// Gets community external ID from source PMS
const propertyExternalReference = await getCommunityExternalId(
  communityId,
  sourcePmsService  // e.g., "entrata", "realPage"
)
```

## File Processing Pipeline

### 1. Data Collection
```typescript
for (const dxSetting of dxSettings) {
  const communityId = dxSetting.communityId.toString()
  
  // Get all spaces for community
  const cursor = await findSpacesByCommunityIdCursor(communityId)
  
  // Get external links and PMS settings
  const [communityExternalLinks, communityDxSettings] = await Promise.all([
    getCommunityExternalLinks(communityId),
    getDxSettingsByCommunityId(communityId)  // Excludes Apartments.com
  ])
}
```

### 2. Data Transformation
```typescript
await cursor.eachAsync(async (space) => {
  // Generate unit data
  const rows = await generateApartmentsDotComRows({
    space,
    communityDxSettings,
    communityExternalLinks
  })
  
  // Generate floor plan data
  if (space.floorPlan?.name) {
    floorPlanDataArray.push(
      await buildFloorPlanData(space, communityDxSettings[0])
    )
  }
}, { parallel: 1000 })  // High concurrency processing
```

### 3. File Upload
```typescript
// S3 staging
await uploadToS3({
  bucket: APARTMENT_DOT_COM_BUCKET,
  key: `apartmentsDotCom/${fileName}`,
  buffer: Buffer.from(JSON.stringify(data))
})

// FTP upload (triggered by S3 event)
await upload({
  host: apartmentHost,
  port: Number(apartmentPort),
  user: apartmentUser,
  password: apartmentPassword,
  fileName: `${APARTMENT_DOT_COM_FILE_NAME}.json`,
  buffer
})
```

## URL Generation

### Web Viewer URLs
- **Unit URLs**: Direct links to Peek unit viewer pages
- **Floor Plan URLs**: Links to floor plan detail pages
- **Amenity URLs**: Links to amenity showcase pages
- **Purpose**: Drives traffic from Apartments.com back to Peek

### Image URL Processing
```typescript
const fixUrl = (url: string) => {
  // Ensures proper URL formatting for Apartments.com
  // Handles relative URLs, missing protocols, etc.
}
```

## Performance Considerations

### High-Volume Processing
- **Parallel Processing**: 1000 concurrent space processing operations
- **Cursor-Based Queries**: Efficient memory usage for large datasets
- **Batch Operations**: Minimizes database round trips

### File Size Management
- **JSON Format**: Efficient serialization for large datasets
- **Compression**: FTP upload handles compression automatically
- **Staging**: S3 intermediate storage for reliability

## Error Handling

### FTP Connection Issues
```typescript
if (!apartmentHost || !apartmentPort || !apartmentUser || !apartmentPassword) {
  throw new Error('Required FTP credentials not found')
}
```

### File Processing Errors
- **Space-Level Errors**: Individual space processing failures don't stop entire export
- **Graceful Degradation**: Missing data handled with fallbacks
- **Logging**: Detailed error tracking for troubleshooting

## Integration Patterns

### ILS vs PMS Distinction
- **PMS Integrations**: Bidirectional data sync (`syncPms: true`)
- **ILS Integrations**: Outbound listing syndication (`syncIls: true`)
- **Apartments.com**: Pure ILS integration (no inbound data)

### Data Dependency Model
1. **Primary PMS**: Entrata, RealPage, Yardi provide core property data
2. **Apartments.com**: Consumes and exports aggregated data
3. **External Links**: Maps Peek spaces to source PMS identifiers

### Lead Generation Flow
1. **Data Export**: Peek → Apartments.com (daily file upload)
2. **Listing Display**: Apartments.com shows properties to prospects
3. **Lead Generation**: Prospects contact properties through Apartments.com
4. **Lead Processing**: Handled by primary PMS integrations (not Apartments.com)

## Monitoring & Maintenance

### File Generation Monitoring
- **Daily Schedule**: Verify file generation completion
- **File Size Validation**: Monitor for unexpected size changes
- **FTP Upload Success**: Confirm successful delivery to Apartments.com

### Data Quality Checks
- **External ID Validation**: Ensure proper PMS mapping
- **URL Accessibility**: Verify generated URLs are functional
- **Image URL Validation**: Check image accessibility and formatting
