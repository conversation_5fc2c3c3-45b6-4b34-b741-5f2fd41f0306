# Yardi Integration Documentation

## Overview
The Yardi integration enables bidirectional data synchronization between Peek and Yardi property management systems through SOAP-based XML web services. This integration handles prospect management, tour scheduling, and property data synchronization.

## Key Components

### 1. Configuration (DX Settings)
Each community using Yardi has a `DXSetting` document with Yardi-specific configuration:

```typescript
interface YardiSetting {
  url: string          // Community-specific Yardi API endpoint (REQUIRED)
  user: string         // Yardi username
  pass: string         // Yardi password
  propertyId: string   // Yardi property identifier
  serverName: string   // Yardi server name
  database: string     // Yardi database name
  entity?: string      // Yardi entity (optional)
  license?: string     // Yardi license (optional, falls back to env var)
  agentName?: {        // Optional agent details
    firstName?: string
    lastName?: string
  }
  transactionSource?: string
}
```

### 2. Core Services

#### Prospect Management
- **Send Prospects**: Pushes prospect data from Peek to Yardi
- **Sync Prospect Status**: Retrieves prospect status updates from Yardi
- **Sync Prospect Events**: Fetches prospect event history and stores in S3

#### Tour Scheduling
- **Schedule SGT**: Creates self-guided tour appointments in Yardi
- **Fetch Event IDs**: Retrieves Yardi event identifiers for tracking

#### Property Data
- **Sync Communities**: Fetches property/unit data from Yardi
- **Fetch Property Objects**: Retrieves property configuration details

### 3. Serverless Functions

#### `syncYardiProspectEvents.ts`
- **Purpose**: Retrieves prospect event history from Yardi and stores in S3
- **Trigger**: SQS messages containing `dxSettingId` and `prospectExternalId`
- **Process**:
  1. Fetches DX settings for the community
  2. Builds XML request for prospect events
  3. Calls Yardi SOAP API
  4. Stores events as JSON files in S3 bucket

### 4. XML Communication
All Yardi communication uses SOAP XML requests through the `ItfILSGuestCard.asmx` endpoint:
- **buildXmlGetProspects**: Retrieves prospect data
- **buildXmlSendProspect**: Sends prospect information
- **buildXmlScheduleSgt**: Schedules self-guided tours
- **buildXmlList**: Fetches property listings

## Important Configuration Notes

### API URL Configuration
⚠️ **Critical**: The `dxSetting.yardi.url` field should **always** be populated with the community-specific Yardi endpoint. 

- **Primary**: `dxSetting.yardi.url` (community-specific, dynamic)
- **Fallback**: `process.env.YARDI_API_URL` (should never be used unless Yardi provides us a generic endpoint)

The `YARDI_API_URL` environment variable exists as a fallback but:ß
- It was only implemented to support a generic URL in case Yardi provided one.
- Each community has its own unique Yardi endpoint URL

### Data Flow
1. **Inbound**: Yardi → Peek (prospect status, events, property data)
2. **Outbound**: Peek → Yardi (new prospects, tour schedules)
3. **Storage**: Event data stored in S3 for analytics and audit trails

## Security
- Credentials stored in encrypted DX settings per community
- SOAP requests use HTTPS endpoints
- No shared API keys across communities
- Each community maintains isolated Yardi connection

## Monitoring
- All operations logged with community context
- Failed requests logged with error details
- S3 storage provides audit trail for prospect events
