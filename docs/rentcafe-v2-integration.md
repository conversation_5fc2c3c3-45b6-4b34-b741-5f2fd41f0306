# RentCafe V2 Integration Documentation

## Overview
RentCafe V2 is the modernized integration with RentCafe property management systems, featuring OAuth2 authentication, improved API endpoints, and enhanced functionality compared to the legacy V1 integration. This integration handles property data synchronization, tour scheduling, and virtual tour management.

## Key Differences from V1

### Authentication
- **V1**: Simple API token authentication via query parameters
- **V2**: OAuth2 Bearer token authentication with automatic token refresh
- **V2**: JWT token validation with expiration handling

### API Structure
- **V1**: Single endpoint with query parameters (`url?apiToken=xxx`)
- **V2**: RESTful endpoints with JSON payloads and proper HTTP headers
- **V2**: Separate marketing and property management API URLs

### Configuration Complexity
- **V1**: Simple `{apiToken, propertyId, url}` configuration
- **V2**: Rich configuration with multiple codes and optional URLs

## Configuration (DX Settings)

```typescript
interface RentCafeV2Setting {
  apiToken: string           // Property-specific API token
  propertyCode: string       // RentCafe property identifier
  propertyId: number         // Numeric property ID
  companyCode: string        // RentCafe company identifier
  timezone: string           // Property timezone for scheduling
  url?: string              // Optional: Property-specific API URL
  marketingUrl?: string     // Optional: Marketing API URL
  sendVirtualTours?: boolean // Enable virtual tour sync
  source?: string           // Lead source identifier
}
```

## Critical URL Configuration

⚠️ **Important**: RentCafe V2 uses a **dual URL system** with intelligent fallbacks:

### Primary API (Property Management)
- **Primary**: `dxSetting.rentCafeV2.url` (property-specific)
- **Fallback**: `secrets.RENT_CAFE_V2_API_URL` (shared endpoint)
- **Used for**: Apartment availability, property data sync

### Marketing API (Appointments)
- **Primary**: `dxSetting.rentCafeV2.marketingUrl` (property-specific)
- **Fallback**: `secrets.RENT_CAFE_V1_MKT_API_URL` (shared endpoint)
- **Used for**: Tour scheduling, appointment management

**Note**: Unlike other integrations, RentCafe V2 fallback URLs are **intentionally shared** across properties and are actively used when property-specific URLs aren't configured.

## Core Services

### 1. Authentication & Token Management
- **OAuth2 Login**: Exchanges credentials for Bearer tokens
- **Token Validation**: JWT expiration checking with auto-refresh
- **Centralized Secrets**: Shared authentication credentials across all properties

### 2. Property Data Synchronization
- **Apartment Availability**: Fetches unit availability and pricing
- **Space Sync**: Updates Peek database with RentCafe property data
- **Status Mapping**: Converts RentCafe unit statuses to Peek availability states

### 3. Tour Management
- **Available Slots**: Retrieves bookable time slots
- **Create Appointments**: Schedules self-guided tours
- **Complete Tours**: Marks tours as completed in RentCafe
- **Tour Types**: Supports both guided tours and self-guided tours (SGT)

### 4. Virtual Tour Integration
- **Media Upload**: Sends virtual tour URLs to RentCafe
- **Unit-Level Tours**: Associates tours with specific units
- **FloorPlan Tours**: Associates tours with floor plans
- **Automatic Publishing**: Controls website visibility

## Serverless Functions

### `syncPMS.ts`
- **Purpose**: Synchronizes property/unit data from RentCafe to Peek
- **Trigger**: SQS messages containing `dxSettingId`
- **Process**:
  1. Loads shared RentCafe V2 secrets
  2. Fetches DX settings for the community
  3. Calls `syncSpaces()` to update property data

### `updateRentCafeV2Token.ts`
- **Purpose**: Refreshes OAuth2 authentication tokens
- **Schedule**: Runs periodically to prevent token expiration
- **Process**:
  1. Performs OAuth2 login
  2. Updates stored token in secrets management

## Authentication Flow

```typescript
// 1. Load shared secrets
await loadRentCafeV2Secrets()

// 2. Validate current token
await validateToken(secrets.CURRENT_TOKEN)

// 3. Auto-refresh if expired
if (tokenExpired) {
  const newToken = await login()
  await updateDXToken(newToken)
}

// 4. Use Bearer token in requests
headers: {
  Authorization: `Bearer ${secrets.CURRENT_TOKEN}`,
  vendor: secrets.RENT_CAFE_V1_VENDOR
}
```

## Error Handling & Retry Logic

### Gateway Features
- **Automatic Retries**: 3 attempts with exponential backoff
- **401 Retry Logic**: Specifically handles authentication failures
- **Status Code Handling**: Treats 204 responses as errors
- **Detailed Error Messages**: Extracts RentCafe-specific error details

### Common Error Patterns
- **204 No Content**: Often indicates invalid property configuration
- **401 Unauthorized**: Token expiration or invalid credentials
- **Validation Errors**: Missing required fields in API requests

## Data Flow

### Inbound (RentCafe → Peek)
1. **Property Data**: Unit availability, pricing, floor plans
2. **Available Slots**: Bookable appointment times
3. **Tour Confirmations**: Appointment creation responses

### Outbound (Peek → RentCafe)
1. **Tour Requests**: Self-guided tour appointments
2. **Tour Completions**: SGT completion notifications
3. **Virtual Tours**: Media URLs for units/floor plans

## Security & Secrets Management

### Centralized Authentication
- **Shared Credentials**: Single OAuth2 client for all properties
- **Token Storage**: Centralized token management with auto-refresh
- **Vendor Headers**: Required vendor identification in all requests

### Property Isolation
- **API Tokens**: Each property has unique API token
- **Company/Property Codes**: Isolate data access per property
- **Optional URLs**: Properties can override default endpoints

## Monitoring & Debugging

### Logging Strategy
- **Request/Response Logging**: Full API call details in debug mode
- **Token Refresh Events**: Authentication state changes
- **Error Context**: Property-specific error information

### Key Metrics
- **Token Refresh Frequency**: Monitor authentication health
- **API Response Times**: Track RentCafe performance
- **Sync Success Rates**: Property data synchronization health
