service: peek-backend-vacancy-serverless

custom:
  sentry:
    enabled:
      prod: true
      dev: false
  esbuild:
    bundle: true
    packager: yarn
    platform: node
    target: node18
    concurrency: 4
    format: cjs
    banner:
      js: |
        const Sentry = require("@sentry/aws-serverless");
        
        const consoleLevels = [];
        if (process.env.ENABLE_SENTRY_CONSOLE_ERROR === "true") {
          consoleLevels.push("error");
        }
        if (process.env.ENABLE_SENTRY_CONSOLE_WARN === "true") {
          consoleLevels.push("warn");
        }

        Sentry.init({
          dsn: process.env.SENTRY_DSN,
          enabled: process.env.SENTRY_ENABLED?.toString() === 'true',
          integrations: [Sentry.captureConsoleIntegration({ levels: consoleLevels })],
        });
        Sentry.setTag("stack_name", "${self:service}-${self:provider.stage}");
    footer:
      js: |
        module.exports = { 
          handler: Sentry.wrapHandler(handler)
        };
provider:
  name: aws
  runtime: nodejs18.x
  stage: ${opt:stage, 'dev'}
  region: 'us-east-1'
  memorySize: 512
  timeout: 10
  logRetentionInDays: 90
  versionFunctions: false
  layers:
    - arn:aws:lambda:us-east-1:943013980633:layer:SentryNodeServerlessSDKv9:11
  environment:
    SENTRY_DSN: ${ssm:/${self:provider.stage}/SENTRY_SERVERLESS_DSN}
    SENTRY_ENABLED: ${self:custom.sentry.enabled.${opt:stage, 'dev'}}
  iamRoleStatements:
    - Effect: Allow
      Action:
        - SQS:SendMessage
        - SQS:DeleteMessage
        - SQS:ReceiveMessage
        - SQS:GetQueueUrl
        - SQS:ListQueues
      Resource: '*'
    - Effect: 'Allow'
      Action:
        - sns:Publish
        - sns:GetTopicAttributes
        - sns:Receive
      Resource: '*'
    - Effect: 'Allow'
      Action:
        - 's3:PutObject'
        - 's3:GetObject'
        - 's3:ListBucket'
        - 's3:DeleteObject'
      Resource: '*'
    - Effect: 'Allow'
      Action:
        - secretsmanager:GetSecretValue
        - secretsmanager:ListSecrets
      Resource: '*'

functions:
  VacancyDataFunction:
    name: ${self:provider.stage}-vacancy-data-function
    handler: src/modules/spacex/serverless/vacancy.handler
    description: 'Info: Calculate marketable days. Flow: SpaceX -> SpaceEventsTopic -> VacancyDataQueue -> VacancyDataFunction'
    timeout: 900
    reservedConcurrency: 5
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
    events:
      - sqs:
          batchSize: 10
          arn:
            Fn::GetAtt:
              - VacancyDataQueue
              - Arn
  SpaceLogsFunction:
    name: ${self:provider.stage}-space-logs-function
    handler: src/modules/spacex/serverless/spaceLogs.handler
    description: 'Info: Get space, generate a log (json) and send to s3. Flow: SpaceX -> SpaceEventsTopic -> SpaceLogsQueue -> SpaceLogsFunction -> SpaceLogsBucket'
    timeout: 900
    reservedConcurrency: 1
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SPACE_LOGS_S3_BUCKET: ${self:provider.stage}-space-logs-bucket
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - SpaceLogsQueue
              - Arn
  DeduplicateFunction:
    name: ${self:provider.stage}-dedupe-notification-function
    handler: src/modules/spacex/serverless/dedupeNotification.handler
    description: 'Info: Send alert to slack when scanner create a new space. Flow: SpaceX -> SpaceEventsTopic -> DeduplicateQueue -> DeduplicateFunction -> SlackNotificationTopic'
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SLACK_NOTIFICATION_TOPIC: ${ssm:/${self:provider.stage}/SLACK_NOTIFICATION_TOPIC}
      PEEK_ENVIRONMENT: ${self:provider.stage}
    reservedConcurrency: 1
    timeout: 900
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - DeduplicateQueue
              - Arn

  AutoScanTrigger:
    name: ${self:provider.stage}-autoScan-trigger
    handler: src/modules/autoScan/serverless/autoScanTrigger.handler
    description: 'Find all enabled communities and call the   autoScan for each community'
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      AUTO_SCAN_QUEUE_URL: { Ref: AutoScanQueue }
    events:
      - schedule:
          rate: cron(0 11 * * ? *)

  AutoScanWorker:
    name: ${self:provider.stage}-autoScan-worker
    handler: src/modules/autoScan/serverless/autoScanWorker.handler
    description: 'Processes and creates automatic scan requests for spaces from a community'
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      AUTO_SCAN_QUEUE_URL: { Ref: AutoScanQueue }
      WORKLOG_JANITOR_ID: ${ssm:/${self:provider.stage}/WORKLOG_JANITOR_ID}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_INTERNAL_API_URL: ${ssm:/${self:provider.stage}/PEEK_INTERNAL_API_URL}
      KLAVYIO_QUEUE_URL: ${ssm:/${self:provider.stage}/KLAVYIO_QUEUE_URL}
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - AutoScanQueue
              - Arn

  ScheduledScansReminderTrigger:
    name: ${self:provider.stage}-scheduled-scans-reminder-trigger
    handler: src/modules/scanRequests/workers/scheduledScansReminder.handler
    description: 'Find all scans scheduled for the next 2 days, and send a reminder to the users (agents + org admins) with access to the community'
    timeout: 900
    environment:
      KLAVYIO_QUEUE_URL: ${ssm:/${self:provider.stage}/KLAVYIO_QUEUE_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      PEEK_INTERNAL_API_URL: ${ssm:/${self:provider.stage}/PEEK_INTERNAL_API_URL}
      PHOTOSHOOT_PREP_GUIDE_URL: ${ssm:/${self:provider.stage}/PHOTOSHOOT_PREP_GUIDE_URL}
    events:
      - schedule:
          rate: cron(0 15 * * ? *)
          enabled: true

  KnockScheduleSGTTour:
    name: ${self:provider.stage}-knock-schedule-sgt-tour
    handler: src/modules/knock/serverless/sgt/schedule.handler
    description: 'Schedule SGT Knock tours'
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      PEEK_ENVIRONMENT: ${self:provider.stage}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - KnockScheduleSGTTourQueue
              - Arn

  KnockRescheduleSGTTour:
    name: ${self:provider.stage}-knock-reschedule-sgt-tour
    handler: src/modules/knock/serverless/sgt/reschedule.handler
    description: 'Reschedule SGT Knock tours'
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      PEEK_ENVIRONMENT: ${self:provider.stage}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - KnockRescheduleSGTTourQueue
              - Arn

  KnockCancelScheduleSGTTour:
    name: ${self:provider.stage}-knock-cancel-schedule-sgt-tour
    handler: src/modules/knock/serverless/sgt/cancelSchedule.handler
    description: 'Cancel Schedule SGT Knock tours'
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      PEEK_ENVIRONMENT: ${self:provider.stage}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - KnockCancelScheduleSGTTourQueue
              - Arn

  KnockSendSummary:
    name: ${self:provider.stage}-send-knock-summary
    handler: src/modules/knock/serverless/sendKnockSummary.handler
    description: 'Send Knock summary to prospects'
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      PEEK_ENVIRONMENT: ${self:provider.stage}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - KnockSendSummaryQueue
              - Arn

  KnockSyncProspectStatus:
    name: ${self:provider.stage}-knock-sync-prospect-status
    handler: src/modules/knock/serverless/syncKnockProspectStatus.handler
    description: 'Sync Knock prospect status'
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      PEEK_ENVIRONMENT: ${self:provider.stage}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - KnockSyncProspectStatusQueue
              - Arn

  cortlandSyncStart:
    handler: src/modules/cortland/serverless/syncCortlandStart.handler
    name: ${self:provider.stage}-cortland-sync-start
    reservedConcurrency: 1
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      DX_SYNC_QUEUE_URL: ${ssm:/${self:provider.stage}/DX_CORTLAND_SYNC_QUEUE_URL}
    timeout: 100
    events:
      - sns:
          arn: arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-dx-exchange-topic
          topicName: ${self:service}-${self:provider.stage}-dx-exchange-topic
          filterPolicy:
            service:
              - cortland

  cortlandSyncWorker:
    handler: src/modules/cortland/serverless/syncCortlandWorker.handler
    name: ${self:provider.stage}-cortland-sync-worker
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      CREATE_PMS_SPACE_TOPIC_QUEUE: ${ssm:/${self:provider.stage}/CREATE_PMS_SPACE_TOPIC_QUEUE}
      CORTLAND_API_URL: ${ssm:/${self:provider.stage}/CORTLAND_API_URL}
      CORTLAND_API_KEY: ${ssm:/${self:provider.stage}/CORTLAND_API_KEY}
    reservedConcurrency: 5
    memorySize: 2048
    timeout: 900
    ephemeralStorageSize: 2048
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - CortlandSyncQueue
              - Arn

  SyncLatchDevicesFunction:
    handler: src/modules/sgt/serverless/syncLatchDevices.handler
    name: ${self:provider.stage}-sync-latch-devices-function
    description: 'Sync Latch devices for a specific DX setting'
    timeout: 300
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      PEEK_ENVIRONMENT: ${self:provider.stage}

  AnyoneHomeSyncProspectStatus:
    name: ${self:provider.stage}-anyonehome-sync-prospect-status
    handler: src/modules/anyoneHome/serverless/syncAnyoneHomeProspectStatus.handler
    description: 'Sync AnyoneHome prospect status'
    reservedConcurrency: 1
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      PEEK_ENVIRONMENT: ${self:provider.stage}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      ANYONE_HOME_SYNC_PROSPECT_STATUS_QUEUE_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_SYNC_PROSPECT_STATUS_QUEUE_URL}
      ANYONE_HOME_SYNC_PROSPECT_STATUS_MAX_NUMBER_OF_MESSAGES: ${ssm:/${self:provider.stage}/ANYONE_HOME_SYNC_PROSPECT_STATUS_MAX_NUMBER_OF_MESSAGES}
    events:
      - schedule:
          rate: cron(0 * * * ? *)
          enabled: true

  RentCafeV2SyncVirtualTours:
    name: ${self:provider.stage}-rentcafev2-sync-virtual-tours
    handler: src/modules/rentCafeV2/serverless/syncVirtualTours.handler
    description: 'Sync (send) RentCafeV2 Virtual Tours'
    reservedConcurrency: 1
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      PEEK_ENVIRONMENT: ${self:provider.stage}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
      RENT_CAFE_V2_SECRETS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS}
      RENT_CAFE_V2_SECRETS_MANAGERS: ${ssm:/${self:provider.stage}/RENT_CAFE_V2_SECRETS_MANAGERS}
    events:
      - schedule:
          rate: cron(0 * * * ? *)
          enabled: true

  AnyoneHomeScheduleSgtTourFunction:
    handler: src/modules/anyoneHome/serverless/sgt/scheduleSgtTour.handler
    name: ${self:provider.stage}-anyonehome-schedule-sgt-tour-serverless
    reservedConcurrency: 5
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - AnyoneHomeScheduleSgtTourQueue
              - Arn
  AnyoneHomeRescheduleSgtTourFunction:
    handler: src/modules/anyoneHome/serverless/sgt/rescheduleSgtTour.handler
    name: ${self:provider.stage}-anyonehome-reschedule-sgt-tour-serverless
    reservedConcurrency: 5
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - AnyoneHomeRescheduleSgtTourQueue
              - Arn
  AnyoneHomeCancelSgtTourFunction:
    handler: src/modules/anyoneHome/serverless/sgt/cancelSgtTour.handler
    name: ${self:provider.stage}-anyoneHome-cancel-sgt-tour-serverless
    reservedConcurrency: 5
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - AnyoneHomeCancelSgtTourQueue
              - Arn
  AnyoneHomeCompleteSgtTour:
    handler: src/modules/anyoneHome/serverless/sgt/completeSgtTour.handler
    name: ${self:provider.stage}-anyonehome-complete-sgt-tour-serverless
    reservedConcurrency: 5
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - AnyoneHomeCompleteSgtTourQueue
              - Arn

  #this should run after consolidateScannerWorkLogs
  SendWorkLogRecapEmailTrigger:
    name: ${self:provider.stage}-send-worklog-recap-email-trigger
    handler: src/modules/communities/serverless/sendWorkLogRecapEmail.handler
    description: 'Send a recap email for the users (agents + org admins) about the scan requests of the last day'
    timeout: 900
    environment:
      KLAVYIO_QUEUE_URL: ${ssm:/${self:provider.stage}/KLAVYIO_QUEUE_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      PEEK_INTERNAL_API_URL: ${ssm:/${self:provider.stage}/PEEK_INTERNAL_API_URL}
      AGENT_DASHBOARD_WEB_URL: ${ssm:/${self:provider.stage}/AGENT_DASHBOARD_WEB_URL}
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
    events:
      - schedule:
        rate: cron(0 13 * * ? *)
        enabled: true

  StoreKlavyioEvent:
    name: ${self:provider.stage}-store-klavyio-event
    handler: src/modules/klavyio/services/storeEvents.handler
    description: 'Store klavyio events'
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - StoreKlavyioEventQueue
              - Arn

  GetKnockApplicationIdsFunction:
    handler: src/scripts/prospects/fix-knock-prospects-without-external-id.handler
    name: ${self:provider.stage}-fix-knock-prospects-without-external-ids
    reservedConcurrency: 1
    timeout: 30
    environment:
      KNOCK_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/KNOCK_FIX_EXTERNAL_LINK_QUEUE_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      KNOCK_BASE_URL: ${ssm:/${self:provider.stage}/KNOCK_BASE_URL}
      KNOCK_API_KEY: ${ssm:/${self:provider.stage}/KNOCK_API_KEY}
      KNOCK_TIMEZONE: ${ssm:/${self:provider.stage}/KNOCK_TIMEZONE}
      PEEK_ENVIRONMENT: ${self:provider.stage}
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - FixKnockApplicationIdsQueue
              - Arn

  GetAnyoneHomeApplicationIdsFunction:
    handler: src/scripts/prospects/fix-anyonehome-prospects-without-id.handler
    name: ${self:provider.stage}-fix-anyonehome-prospects-without-external-ids
    reservedConcurrency: 1
    timeout: 900
    environment:
      ANYONE_HOME_FIX_EXTERNAL_LINK_QUEUE_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_FIX_EXTERNAL_LINK_QUEUE_URL}
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      ANYONE_HOME_API_URL: ${ssm:/${self:provider.stage}/ANYONE_HOME_API_URL}
      ANYONE_HOME_USERNAME: ${ssm:/${self:provider.stage}/ANYONE_HOME_USERNAME}
      ANYONE_HOME_PASSWORD: ${ssm:/${self:provider.stage}/ANYONE_HOME_PASSWORD}
      ANYONE_HOME_FIX_EXTERNAL_LINK_MAX_NUMBER_OF_MESSAGES: ${ssm:/${self:provider.stage}/ANYONE_HOME_FIX_EXTERNAL_LINK_MAX_NUMBER_OF_MESSAGES}
    events:
      - schedule:
          rate: cron(0 * * * ? *)
          enabled: true

  EngrainCaptureData:
    handler: src/modules/engrain/serverless/captureEngrainData.handler
    name: ${self:provider.stage}-engrain-capture-data
    reservedConcurrency: 1
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      ENGRAIN_METADATA: ${self:provider.stage}-engrain-metadata
    events:
      - schedule:
          rate: rate(5 hours)

  EngrainGetByFloorPlanFunction:
    handler: src/modules/engrain/serverless/getByFloorplan.handler
    name: ${self:provider.stage}-engrain-get-by-floorplan-function
    reservedConcurrency: 5
    timeout: 300
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      ENGRAIN_METADATA: ${self:provider.stage}-engrain-metadata
      DX_ENGRAIN_SEND_VIRTUAL_TOUR_QUEUE_URL: https://sqs.us-east-1.amazonaws.com/${aws:accountId}/${self:provider.stage}-engrain-send-virtual-tour-queue
      WEB_VIEWER_BASE_URL: ${ssm:/${self:provider.stage}/WEB_VIEWER_BASE_URL}
    events:
      - sqs:
          batchSize: 5
          functionResponseType: 'ReportBatchItemFailures'
          arn:
            Fn::GetAtt:
              - EngrainGetFloorplansQueue
              - Arn

  RealPageUnavailableUnitsFunction:
    handler: src/modules/realPage/serverless/unavailableUnits.handler
    name: ${self:provider.stage}-realpage-unavailable-units-function
    reservedConcurrency: 1
    timeout: 900
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      SPACE_CHANGE_TOPIC: ${ssm:/${self:provider.stage}/SPACE_CHANGE_TOPIC}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - RealPageUnavailableUnitsQueue
              - Arn

  CategorizerAmenity:
    handler: src/modules/communities/serverless/categorizer/amenity.handler
    name: ${self:provider.stage}-categorizer-amenity
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      OPEN_AI_API_KEY: ${ssm:/${self:provider.stage}/OPEN_AI_API_KEY}
      CATEGORIZER_AI_ENDPOINT: ${ssm:/${self:provider.stage}/CATEGORIZER_AI_ENDPOINT}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - CategorizerAmenityQueue
              - Arn
  CategorizerNode:
    handler: src/modules/communities/serverless/categorizer/node.handler
    name: ${self:provider.stage}-categorizer-node
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      OPEN_AI_API_KEY: ${ssm:/${self:provider.stage}/OPEN_AI_API_KEY}
      CATEGORIZER_AI_ENDPOINT: ${ssm:/${self:provider.stage}/CATEGORIZER_AI_ENDPOINT}
    events:
      - sqs:
          arn:
            Fn::GetAtt:
              - CategorizerNodeQueue
              - Arn
  SyncFunnelProspectStatusFunction:
    handler: src/modules/funnel/serverless/syncFunnelProspectStatus.handler
    name: ${self:provider.stage}-sync-funnel-prospect-status-function
    reservedConcurrency: 1
    timeout: 100
    environment:
      MONGODB_URL: ${ssm:/${self:provider.stage}/MONGODB_URL}
      FUNNEL_CUSTOMER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_CUSTOMER_API_URL}
      FUNNEL_PARTNER_API_URL: ${ssm:/${self:provider.stage}/FUNNEL_PARTNER_API_URL}
    events:
      - sqs:
          batchSize: 1
          arn:
            Fn::GetAtt:
              - SyncFunnelProspectStatusQueue
              - Arn

resources:
  Resources:
    # topics
    SpaceEventsTopic:
      Type: AWS::SNS::Topic
      Properties:
        TopicName: ${self:provider.stage}-space-events-topic

    # policies
    SpaceEventsPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - VacancyDataQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SpaceEventsTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - SpaceLogsQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SpaceEventsTopic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - DeduplicateQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn':
                    Ref: SpaceEventsTopic
        Queues:
          - Ref: VacancyDataQueue
          - Ref: SpaceLogsQueue
          - Ref: DeduplicateQueue

    SGTPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - KnockScheduleSGTTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn': arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sgt-event-topic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - KnockRescheduleSGTTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn': arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sgt-event-topic
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - KnockCancelScheduleSGTTourQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn': arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sgt-event-topic
        Queues:
          - Ref: KnockScheduleSGTTourQueue
          - Ref: KnockRescheduleSGTTourQueue
          - Ref: KnockCancelScheduleSGTTourQueue

    KnockSendSummaryPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - KnockSendSummaryQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn': arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-pi-summary-topic
        Queues:
          - Ref: KnockSendSummaryQueue

    SyncKnockProspectSnsToQueueSQSPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - KnockSyncProspectStatusQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn': arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sync-prospect-topic
        Queues:
          - Ref: KnockSyncProspectStatusQueue

    SyncProspectsSnsToQueueSQSPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - SyncFunnelProspectStatusQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn': arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sync-prospect-topic
        Queues:
          - Ref: SyncFunnelProspectStatusQueue

    SyncAnyoneHomeProspectSnsToQueueSQSPolicy:
      Type: AWS::SQS::QueuePolicy
      Properties:
        PolicyDocument:
          Version: '2012-10-17'
          Statement:
            - Sid: 'allow-sns-messages'
              Effect: 'Allow'
              Principal:
                Service:
                  - 'sns.amazonaws.com'
              Resource:
                Fn::GetAtt:
                  - AnyoneHomeSyncProspectStatusQueue
                  - Arn
              Action: 'SQS:SendMessage'
              Condition:
                ArnEquals:
                  'aws:SourceArn': arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sync-prospect-topic
        Queues:
          - Ref: AnyoneHomeSyncProspectStatusQueue

    # queues & subscriptions
    VacancyDataQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-vacancy-data-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - VacancyDataQueueDLQ
              - Arn
          maxReceiveCount: 3

    VacancyDataQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-vacancy-data-queue-dlq

    VacancyDataEventSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - VacancyDataQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SpaceEventsTopic
        FilterPolicy:
          eventType:
            - CREATED
            - UPDATED
          spaceType:
            - unit

    SpaceLogsQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-space-logs-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SpaceLogsQueueDLQ
              - Arn
          maxReceiveCount: 3

    SpaceLogsQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-space-logs-queue-dlq

    SpaceLogsEventSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - SpaceLogsQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SpaceEventsTopic
        FilterPolicy:
          eventType:
            - CREATED
            - UPDATED
          spaceType:
            - unit

    DeduplicateQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-deduplicate-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - DeduplicateQueueDLQ
              - Arn
          maxReceiveCount: 3

    DeduplicateQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-deduplicate-queue-dlq

    DeduplicateEventSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - DeduplicateQueue
            - Arn
        Protocol: sqs
        TopicArn:
          Ref: SpaceEventsTopic
        FilterPolicy:
          eventType:
            - CREATED
          spaceType:
            - unit

    AutoScanQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-autoScan-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - AutoScanQueueDLQ
              - Arn
          maxReceiveCount: 3

    AutoScanQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-autoScan-queue-dlq

    StoreKlavyioEventQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-store-klavyio-event-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - StoreKlavyioEventQueueDLQ
              - Arn
          maxReceiveCount: 3

    StoreKlavyioEventQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-store-klavyio-event-queue-dlq

    KnockScheduleSGTTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-schedule-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - KnockScheduleSGTTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    KnockScheduleSGTTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-schedule-sgt-tour-queue-dlq
    KnockScheduleSGTTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - KnockScheduleSGTTourQueue
            - Arn
        Protocol: sqs
        TopicArn: arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sgt-event-topic
        FilterPolicy:
          service:
            - knock
          sgtType:
            - SCHEDULE_TOUR

    KnockRescheduleSGTTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-reschedule-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - KnockRescheduleSGTTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    KnockRescheduleSGTTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-reschedule-sgt-tour-queue-dlq
    KnockRescheduleSGTTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - KnockRescheduleSGTTourQueue
            - Arn
        Protocol: sqs
        TopicArn: arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sgt-event-topic
        FilterPolicy:
          service:
            - knock
          sgtType:
            - RESCHEDULE_TOUR

    KnockCancelScheduleSGTTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-cancel-schedule-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - KnockCancelScheduleSGTTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    KnockCancelScheduleSGTTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-cancel-schedule-sgt-tour-queue-dlq
    KnockCancelScheduleSGTTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - KnockCancelScheduleSGTTourQueue
            - Arn
        Protocol: sqs
        TopicArn: arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sgt-event-topic
        FilterPolicy:
          service:
            - knock
          sgtType:
            - CANCEL_TOUR

    KnockSendSummaryQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-send-summary-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - KnockSendSummaryQueueDLQ
              - Arn
          maxReceiveCount: 3
    KnockSendSummaryQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-send-summary-queue-dlq
    KnockSendSummarySubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - KnockSendSummaryQueue
            - Arn
        Protocol: sqs
        TopicArn: arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-pi-summary-topic

    KnockSyncProspectStatusQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-sync-prospect-status-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - KnockSyncProspectStatusQueueDLQ
              - Arn
          maxReceiveCount: 1
    KnockSyncProspectStatusQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-knock-sync-prospect-status-queue-dlq

    KnockSyncProspectStatusSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - KnockSyncProspectStatusQueue
            - Arn
        Protocol: sqs
        TopicArn: arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sync-prospect-topic
        FilterPolicy:
          service:
            - knock
    AnyoneHomeScheduleSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-anyonehome-schedule-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - AnyoneHomeScheduleSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    AnyoneHomeScheduleSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-anyonehome-schedule-sgt-tour-queue-dlq
    AnyoneHomeScheduleSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - AnyoneHomeScheduleSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn: arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sgt-event-topic
        FilterPolicy:
          service:
            - anyoneHome
          sgtType:
            - SCHEDULE_TOUR

    AnyoneHomeRescheduleSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-anyonehome-reschedule-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - AnyoneHomeRescheduleSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    AnyoneHomeRescheduleSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-anyonehome-reschedule-sgt-tour-queue-dlq
    AnyoneHomeRescheduleSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - AnyoneHomeRescheduleSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn: arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sgt-event-topic
        FilterPolicy:
          service:
            - anyoneHome
          sgtType:
            - RESCHEDULE_TOUR

    AnyoneHomeCancelSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-anyonehome-cancel-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - AnyoneHomeCancelSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    AnyoneHomeCancelSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-anyonehome-cancel-sgt-tour-queue-dlq
    AnyoneHomeCancelSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - AnyoneHomeCancelSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn: arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sgt-event-topic
        FilterPolicy:
          service:
            - anyoneHome
          sgtType:
            - CANCEL_TOUR

    AnyoneHomeCompleteSgtTourQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-anyonehome-complete-sgt-tour-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - AnyoneHomeCompleteSgtTourQueueDLQ
              - Arn
          maxReceiveCount: 3
    AnyoneHomeCompleteSgtTourQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-anyonehome-complete-sgt-tour-queue-dlq
    AnyoneHomeCompleteSgtTourSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - AnyoneHomeCompleteSgtTourQueue
            - Arn
        Protocol: sqs
        TopicArn: arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sgt-event-topic
        FilterPolicy:
          service:
            - anyoneHome
          sgtType:
            - COMPLETE_TOUR

    AnyoneHomeSyncProspectStatusQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-anyonehome-sync-prospect-status-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - AnyoneHomeSyncProspectStatusQueueDLQ
              - Arn
          maxReceiveCount: 1
    AnyoneHomeSyncProspectStatusQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-anyonehome-sync-prospect-status-queue-dlq
    AnyoneHomeSyncProspectStatusSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - AnyoneHomeSyncProspectStatusQueue
            - Arn
        Protocol: sqs
        TopicArn: arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sync-prospect-topic
        FilterPolicy:
          service:
            - anyoneHome

    CortlandSyncQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-cortland-sync-queue
        VisibilityTimeout: 1800
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - CortlandSyncQueueDLQ
              - Arn
          maxReceiveCount: 3
    CortlandSyncQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-cortland-sync-queue-dlq

    FixKnockApplicationIdsQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-fix-knock-application-ids-queue
        VisibilityTimeout: 40
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - FixKnockApplicationIdsQueueDLQ
              - Arn
          maxReceiveCount: 1
    FixKnockApplicationIdsQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-fix-knock-application-ids-queue-dlq

    FixAnyoneHomeApplicationIdsQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-fix-anyonehome-application-ids-queue
        VisibilityTimeout: 40
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - FixAnyoneHomeApplicationIdsQueueDLQ
              - Arn
          maxReceiveCount: 1
    FixAnyoneHomeApplicationIdsQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-fix-anyonehome-application-ids-queue-dlq

    EngrainMetadataBucket:
      Type: AWS::S3::Bucket
      Properties:
        BucketName: ${self:provider.stage}-engrain-metadata
        AccessControl: Private

    EngrainGetFloorplansQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-engrain-get-floorplans-queue
        VisibilityTimeout: 800
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - EngrainGetFloorplansQueueDLQ
              - Arn
          maxReceiveCount: 3
    EngrainGetFloorplansQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-engrain-get-floorplans-queue-dlq

    RealPageUnavailableUnitsQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-realpage-unavailable-units-queue
        VisibilityTimeout: 900
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - RealPageUnavailableUnitsQueueDLQ
              - Arn
          maxReceiveCount: 3
    RealPageUnavailableUnitsQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-realpage-unavailable-units-queue-dlq

    CategorizerAmenityQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-categorizer-amenity-queue
        VisibilityTimeout: 900
    CategorizerNodeQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-categorizer-node-queue
        VisibilityTimeout: 900

    SyncFunnelProspectStatusQueue:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-funnel-prospect-queue
        VisibilityTimeout: 1800
        RedrivePolicy:
          deadLetterTargetArn:
            Fn::GetAtt:
              - SyncFunnelProspectStatusQueueDLQ
              - Arn
          maxReceiveCount: 3
    SyncFunnelProspectStatusQueueDLQ:
      Type: AWS::SQS::Queue
      Properties:
        QueueName: ${self:provider.stage}-sync-funnel-prospect-queue-dlq
    SyncFunnelProspectSubscription:
      Type: AWS::SNS::Subscription
      Properties:
        Endpoint:
          Fn::GetAtt:
            - SyncFunnelProspectStatusQueue
            - Arn
        Protocol: sqs
        TopicArn: arn:aws:sns:${self:provider.region}:${aws:accountId}:${self:provider.stage}-sync-prospect-topic
        FilterPolicy:
          service:
            - funnel

package:
  individually: true

plugins:
  - serverless-esbuild
  - ./serverless/sentry-log-error.js
